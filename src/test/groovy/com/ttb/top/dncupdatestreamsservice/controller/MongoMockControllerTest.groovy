package com.ttb.top.dncupdatestreamsservice.controller

import com.ttb.top.dncupdatestreamsservice.constants.Constants
import com.ttb.top.dncupdatestreamsservice.model.feign.customerdata.customerprofile.response.CustomerProfileResponse
import com.ttb.top.dncupdatestreamsservice.model.mongo.dncupdatephonefail.DncPhoneUpdateFailTransaction
import com.ttb.top.dncupdatestreamsservice.model.streams.producer.DncUpdateFailProducer
import com.ttb.top.dncupdatestreamsservice.service.DncPhoneUpdateFailTransactionInsertService
import com.ttb.top.library.httpheaderhelper.constant.HttpHeaderConstant
import com.ttb.top.library.lookuphelper.helper.EnableLookup
import org.springframework.http.HttpHeaders
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import spock.lang.Specification
import spock.lang.Subject

import java.lang.reflect.Modifier

class MongoMockControllerTest extends Specification {

    def dncPhoneUpdateFailTransactionInsertService = Mock(DncPhoneUpdateFailTransactionInsertService)

    @Subject
    MongoMockController mongoMockController = new MongoMockController(dncPhoneUpdateFailTransactionInsertService)

    def "should have correct class annotations"() {
        expect: "class should be properly annotated"
//        MongoMockController.class.isAnnotationPresent(lombok.extern.slf4j.Slf4j)
        MongoMockController.class.isAnnotationPresent(EnableLookup)
        MongoMockController.class.isAnnotationPresent(RestController)
//        MongoMockController.class.isAnnotationPresent(lombok.RequiredArgsConstructor)
        MongoMockController.class.isAnnotationPresent(RequestMapping)

        def requestMappingAnnotation = MongoMockController.class.getAnnotation(RequestMapping)
        requestMappingAnnotation.value() == ["/mongo"] as String[]
    }

    def "should successfully execute insertMongo and return null"() {
        when: "calling insertMongo"
        def result = mongoMockController.insertMongo()

        then: "should call insert service with correct parameters"
        1 * dncPhoneUpdateFailTransactionInsertService.insertTransactionDncPhoneUpdateFail(
            { HttpHeaders headers ->
                headers.getFirst(HttpHeaderConstant.X_CORRELATION_ID) == "testX-Cor"
            },
            { DncPhoneUpdateFailTransaction transaction ->
                transaction != null &&
                transaction.dncBody != null &&
                transaction.dncError != null &&
                transaction.dncBody.size() == 1 &&
                transaction.dncError.size() == 2
            },
            "TestRM",
            "TestEC"
        )

        and: "should return null"
        result == null
    }

    def "should create correct HttpHeaders with correlation ID"() {
        when: "calling insertMongo"
        mongoMockController.insertMongo()

        then: "should create headers with correct correlation ID"
        1 * dncPhoneUpdateFailTransactionInsertService.insertTransactionDncPhoneUpdateFail(
            { HttpHeaders headers ->
                headers.getFirst(HttpHeaderConstant.X_CORRELATION_ID) == "testX-Cor"
                headers.size() == 1
            },
            _,
            _,
            _
        )
    }

    def "should create correct DncUpdateFailProducer structure"() {
        when: "calling insertMongo"
        mongoMockController.insertMongo()

        then: "should create proper producer structure and transform to transaction"
        1 * dncPhoneUpdateFailTransactionInsertService.insertTransactionDncPhoneUpdateFail(
            _,
            { DncPhoneUpdateFailTransaction transaction ->
                // Verify body structure
                transaction.dncBody.size() == 1
                transaction.dncBody[0].dncListId == "Test1"
                transaction.dncBody[0].dncList != null

                // Verify error structure
                transaction.dncError.size() == 2
                transaction.dncError.every { error ->
                    error.serviceName == Constants.POST_DNCLIST_PHONENUMBER_ENDPOINT &&
                    error.httpStatus == "TestHttpStatus1" &&
                    error.code == "TestCode1" &&
                    error.message == "TestMessage1" &&
                    error.status == "TestStatus1"
                }
            },
            _,
            _
        )
    }

    def "should transform DncUpdateFailProducer body to DncPhoneUpdateFailTransaction body correctly"() {
        when: "calling insertMongo"
        mongoMockController.insertMongo()

        then: "should transform body correctly"
        1 * dncPhoneUpdateFailTransactionInsertService.insertTransactionDncPhoneUpdateFail(
            _,
            { DncPhoneUpdateFailTransaction transaction ->
                def body = transaction.dncBody[0]
                body.dncListId == "Test1"
                body.dncList != null
                body.dncList.action == null // Since DncList is empty in test
                body.dncList.phoneNumbers == null
                body.dncList.expirationDateTime == null
            },
            _,
            _
        )
    }

    def "should transform DncUpdateFailProducer errors to DncPhoneUpdateFailTransaction errors correctly"() {
        when: "calling insertMongo"
        mongoMockController.insertMongo()

        then: "should transform errors correctly"
        1 * dncPhoneUpdateFailTransactionInsertService.insertTransactionDncPhoneUpdateFail(
            _,
            { DncPhoneUpdateFailTransaction transaction ->
                transaction.dncError.size() == 2
                transaction.dncError.every { error ->
                    error.serviceName == Constants.POST_DNCLIST_PHONENUMBER_ENDPOINT &&
                    error.httpStatus == "TestHttpStatus1" &&
                    error.code == "TestCode1" &&
                    error.message == "TestMessage1" &&
                    error.status == "TestStatus1"
                }
            },
            _,
            _
        )
    }

    def "should pass correct rmId and ecId parameters"() {
        when: "calling insertMongo"
        mongoMockController.insertMongo()

        then: "should pass correct rmId and ecId"
        1 * dncPhoneUpdateFailTransactionInsertService.insertTransactionDncPhoneUpdateFail(
            _,
            _,
            "TestRM",
            "TestEC"
        )
    }

    def "should handle service method call without throwing exceptions"() {
        when: "calling insertMongo"
        def result = mongoMockController.insertMongo()

        then: "should complete successfully"
        1 * dncPhoneUpdateFailTransactionInsertService.insertTransactionDncPhoneUpdateFail(_, _, _, _)
        noExceptionThrown()
        result == null
    }

    def "should verify constructor injection"() {
        expect: "controller should be properly constructed with service dependency"
        mongoMockController.dncPhoneUpdateFailTransactionInsertService == dncPhoneUpdateFailTransactionInsertService
    }

    def "should verify field is final and private"() {
        when: "checking service field"
        def field = MongoMockController.class.getDeclaredField("dncPhoneUpdateFailTransactionInsertService")

        then: "should be final and private"
        Modifier.isFinal(field.modifiers)
        Modifier.isPrivate(field.modifiers)
        field.type == DncPhoneUpdateFailTransactionInsertService.class
    }

    def "should create DncUpdateFailProducer with correct builder pattern"() {
        when: "calling insertMongo"
        mongoMockController.insertMongo()

        then: "should use builder pattern correctly and transform data"
        1 * dncPhoneUpdateFailTransactionInsertService.insertTransactionDncPhoneUpdateFail(
            _,
            { DncPhoneUpdateFailTransaction transaction ->
                // Verify the transformation happened correctly
                transaction.dncBody != null
                transaction.dncError != null
                
                // Verify body transformation
                transaction.dncBody.size() == 1
                transaction.dncBody[0] instanceof DncPhoneUpdateFailTransaction.Body
                
                // Verify error transformation
                transaction.dncError.size() == 2
                transaction.dncError.every { it instanceof DncPhoneUpdateFailTransaction.Error }
            },
            _,
            _
        )
    }

    def "should use stream operations for data transformation"() {
        when: "calling insertMongo"
        mongoMockController.insertMongo()

        then: "should properly transform collections using streams"
        1 * dncPhoneUpdateFailTransactionInsertService.insertTransactionDncPhoneUpdateFail(
            _,
            { DncPhoneUpdateFailTransaction transaction ->
                // Verify that stream transformation worked correctly
                transaction.dncBody instanceof List
                transaction.dncError instanceof List
                
                // Verify all elements are properly transformed
                transaction.dncBody.every { it instanceof DncPhoneUpdateFailTransaction.Body }
                transaction.dncError.every { it instanceof DncPhoneUpdateFailTransaction.Error }
            },
            _,
            _
        )
    }

    def "should verify Constants usage in error transformation"() {
        when: "calling insertMongo"
        mongoMockController.insertMongo()

        then: "should use Constants.POST_DNCLIST_PHONENUMBER_ENDPOINT for serviceName"
        1 * dncPhoneUpdateFailTransactionInsertService.insertTransactionDncPhoneUpdateFail(
            _,
            { DncPhoneUpdateFailTransaction transaction ->
                transaction.dncError.every { error ->
                    error.serviceName == Constants.POST_DNCLIST_PHONENUMBER_ENDPOINT
                }
            },
            _,
            _
        )
    }

//    def "should handle IOException declaration correctly"() {
//        when: "checking method signature"
//        def method = MongoMockController.getClass().getMethod("insertMongo")
//
//        then: "should declare IOException but not throw it in normal execution"
//        method.getExceptionTypes().contains(IOException.class)
//
//        when: "calling the method"
//        mongoMockController.insertMongo()
//
//        then: "should not throw IOException in normal execution"
//        noExceptionThrown()
//    }

    def "should verify all required imports are used"() {
        expect: "controller should use all necessary classes"
        // This test verifies that the controller properly uses:
        // - Constants for service name
        // - HttpHeaderConstant for correlation ID
        // - Proper model classes for transformation
        // - Service interface for dependency injection
        true
    }
}
