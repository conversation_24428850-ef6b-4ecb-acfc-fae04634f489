package com.ttb.top.dncupdatestreamsservice.service.implement

import com.fasterxml.jackson.core.JsonProcessingException
import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.ObjectMapper
import com.ttb.top.dncupdatestreamsservice.constants.Constants
import com.ttb.top.dncupdatestreamsservice.feign.customerdata.CustomerDataServiceFeignClient
import com.ttb.top.dncupdatestreamsservice.model.feign.customerdata.customerprofile.request.CustomerProfileRequest
import com.ttb.top.dncupdatestreamsservice.model.feign.customerdata.customerprofile.response.CustomerProfileResponse
import com.ttb.top.dncupdatestreamsservice.model.feign.customerdata.updatephonenumbers.request.UpdatePhoneNumbersRequest
import com.ttb.top.dncupdatestreamsservice.model.mongo.dncupdatephonefail.DncPhoneUpdateFailTransaction
import com.ttb.top.dncupdatestreamsservice.model.streams.consumer.DncUpdateConsume
import com.ttb.top.dncupdatestreamsservice.model.streams.producer.DncUpdateFailProducer
import com.ttb.top.dncupdatestreamsservice.service.CustomerCacheService
import com.ttb.top.dncupdatestreamsservice.service.DncPhoneUpdateFailTransactionInsertService
import com.ttb.top.dncupdatestreamsservice.service.DncUpdateConsumerService
import com.ttb.top.dncupdatestreamsservice.streams.producer.EventHubProducer
import com.ttb.top.library.commonmodel.model.ResponseModel
import com.ttb.top.library.exceptionmodel.constant.ResponseCodeEnum
import org.springframework.http.HttpHeaders
import org.springframework.kafka.support.Acknowledgment
import org.springframework.stereotype.Service
import spock.lang.Specification
import spock.lang.Subject

class DncUpdateConsumerServiceImplTest extends Specification {

    def objectMapper = Mock(ObjectMapper)
    def customerDataServiceFeignClient = Mock(CustomerDataServiceFeignClient)
    def customerCacheService = Mock(CustomerCacheService)
    def dncPhoneUpdateFailTransactionInsertService = Mock(DncPhoneUpdateFailTransactionInsertService)
    def retryUpdateDncPhoneService = Mock(RetryUpdateDncPhoneServiceImpl)
    def producer = Mock(EventHubProducer)
    def acknowledgment = Mock(Acknowledgment)

    @Subject
    DncUpdateConsumerServiceImpl service = new DncUpdateConsumerServiceImpl(
        objectMapper,
        customerDataServiceFeignClient,
        customerCacheService,
        dncPhoneUpdateFailTransactionInsertService,
        retryUpdateDncPhoneService,
        producer
    )

    def "should have correct class annotations"() {
        expect: "class should be properly annotated"
//        DncUpdateConsumerServiceImpl.class.isAnnotationPresent(lombok.extern.slf4j.Slf4j)
        DncUpdateConsumerServiceImpl.class.isAnnotationPresent(Service)
//        DncUpdateConsumerServiceImpl.class.isAnnotationPresent(lombok.RequiredArgsConstructor)
    }

    def "should implement DncUpdateConsumerService interface"() {
        expect: "service should implement the interface"
        service instanceof DncUpdateConsumerService
    }

    def "should successfully process message with customer in cache"() {
        given: "valid message and cached customer"
        def message = '{"headers": "{}", "dncUpdateConsumer": "{}"}'
        def headers = new HttpHeaders()
        def dncUpdateConsume = createMockDncUpdateConsume()
        def customerProfile = createMockCustomerProfile()
        def dncUpdateFailProducer = createMockDncUpdateFailProducer()

        and: "mock setup"
        objectMapper.readValue(message, _ as TypeReference) >> ["headers": "{}", "dncUpdateConsumer": "{}"]
        objectMapper.readValue("{}", HttpHeaders.class) >> headers
        objectMapper.readValue("{}", DncUpdateConsume.class) >> dncUpdateConsume
        customerCacheService.getCustomer("testRmId") >> customerProfile
        retryUpdateDncPhoneService.callUpdateDncPhoneWithRetry(_, _, _, _, _) >> dncUpdateFailProducer

        when: "processing message"
        service.processMessage(message, acknowledgment)

        then: "should process successfully"
        1 * customerCacheService.getCustomer("testRmId")
        1 * retryUpdateDncPhoneService.callUpdateDncPhoneWithRetry(_, _, _, _, _)
        1 * dncPhoneUpdateFailTransactionInsertService.insertTransactionDncPhoneUpdateFail(_, _, _, _)
        noExceptionThrown()
    }

    def "should fetch customer profile when not in cache"() {
        given: "valid message and no cached customer"
        def message = '{"headers": "{}", "dncUpdateConsumer": "{}"}'
        def headers = new HttpHeaders()
        def dncUpdateConsume = createMockDncUpdateConsume()
        def customerProfile = createMockCustomerProfile()
        def responseModel = createMockResponseModel(customerProfile, "0000")
        def dncUpdateFailProducer = createMockDncUpdateFailProducer()

        and: "mock setup"
        objectMapper.readValue(message, _ as TypeReference) >> ["headers": "{}", "dncUpdateConsumer": "{}"]
        objectMapper.readValue("{}", HttpHeaders.class) >> headers
        objectMapper.readValue("{}", DncUpdateConsume.class) >> dncUpdateConsume
        customerCacheService.getCustomer("testRmId") >> null
        customerDataServiceFeignClient.customerProfile(_, _) >> responseModel
        retryUpdateDncPhoneService.callUpdateDncPhoneWithRetry(_, _, _, _, _) >> dncUpdateFailProducer

        when: "processing message"
        service.processMessage(message, acknowledgment)

        then: "should fetch and cache customer profile"
        1 * customerCacheService.getCustomer("testRmId")
        1 * customerDataServiceFeignClient.customerProfile(_, _)
        1 * customerCacheService.setCustomer("testRmId", customerProfile)
        1 * retryUpdateDncPhoneService.callUpdateDncPhoneWithRetry(_, _, _, _, _)
        1 * dncPhoneUpdateFailTransactionInsertService.insertTransactionDncPhoneUpdateFail(_, _, _, _)
    }

    def "should handle customer profile service error"() {
        given: "valid message and customer service error"
        def message = '{"headers": "{}", "dncUpdateConsumer": "{}"}'
        def headers = new HttpHeaders()
        def dncUpdateConsume = createMockDncUpdateConsume()
        def customerProfile = createMockCustomerProfile()
        def responseModel = createMockResponseModel(customerProfile, "9999")
        def dncUpdateFailProducer = createMockDncUpdateFailProducer()

        and: "mock setup"
        objectMapper.readValue(message, _ as TypeReference) >> ["headers": "{}", "dncUpdateConsumer": "{}"]
        objectMapper.readValue("{}", HttpHeaders.class) >> headers
        objectMapper.readValue("{}", DncUpdateConsume.class) >> dncUpdateConsume
        customerCacheService.getCustomer("testRmId") >> null
        customerDataServiceFeignClient.customerProfile(_, _) >> responseModel
        retryUpdateDncPhoneService.callUpdateDncPhoneWithRetry(_, _, _, _, _) >> dncUpdateFailProducer

        when: "processing message"
        service.processMessage(message, acknowledgment)

        then: "should handle error and insert fail transaction"
        1 * customerDataServiceFeignClient.customerProfile(_, _)
        2 * dncPhoneUpdateFailTransactionInsertService.insertTransactionDncPhoneUpdateFail(_, _, _, _)
        1 * customerCacheService.setCustomer("testRmId", customerProfile)
    }

    def "should use ecId when rmId is null"() {
        given: "message with ecId only"
        def message = '{"headers": "{}", "dncUpdateConsumer": "{}"}'
        def headers = new HttpHeaders()
        def dncUpdateConsume = createMockDncUpdateConsumeWithEcId()
        def customerProfile = createMockCustomerProfile()

        and: "mock setup"
        objectMapper.readValue(message, _ as TypeReference) >> ["headers": "{}", "dncUpdateConsumer": "{}"]
        objectMapper.readValue("{}", HttpHeaders.class) >> headers
        objectMapper.readValue("{}", DncUpdateConsume.class) >> dncUpdateConsume
        customerCacheService.getCustomer("testEcId") >> customerProfile
        retryUpdateDncPhoneService.callUpdateDncPhoneWithRetry(_, _, _, _, _) >> createMockDncUpdateFailProducer()

        when: "processing message"
        service.processMessage(message, acknowledgment)

        then: "should use ecId for cache lookup"
        1 * customerCacheService.getCustomer("testEcId")
    }

    def "should handle JsonProcessingException"() {
        given: "invalid JSON message"
        def message = "invalid json"
        objectMapper.readValue(message, _ as TypeReference) >> { throw new JsonProcessingException("Invalid JSON") {} }

        when: "processing invalid message"
        service.processMessage(message, acknowledgment)

        then: "should throw RuntimeException"
        thrown(RuntimeException)
    }

    def "should filter phone numbers correctly"() {
        given: "customer profile with various phone types"
        def customerProfile = createCustomerProfileWithPhones()
        
        when: "calling listPhoneNumber method"
        def result = service.listPhoneNumber(customerProfile)

        then: "should return only M, B, R phone types"
        result.length == 3
        result.contains("0812345678") // M type
        result.contains("0823456789") // B type  
        result.contains("0834567890") // R type
        !result.contains("0845678901") // H type should be filtered out
    }

    def "should handle null phone numbers in filtering"() {
        given: "customer profile with null phone numbers"
        def customerProfile = createCustomerProfileWithNullPhones()
        
        when: "calling listPhoneNumber method"
        def result = service.listPhoneNumber(customerProfile)

        then: "should filter out null phone numbers"
        result.length == 1
        result[0] == "0812345678"
    }

    def "should build UpdatePhoneNumbersRequest with expiration date"() {
        given: "DNC update with expiration date"
        def message = '{"headers": "{}", "dncUpdateConsumer": "{}"}'
        def headers = new HttpHeaders()
        def dncUpdateConsume = createMockDncUpdateConsumeWithExpiration()
        def customerProfile = createMockCustomerProfile()
        def dncUpdateFailProducer = createMockDncUpdateFailProducer()

        and: "mock setup"
        objectMapper.readValue(message, _ as TypeReference) >> ["headers": "{}", "dncUpdateConsumer": "{}"]
        objectMapper.readValue("{}", HttpHeaders.class) >> headers
        objectMapper.readValue("{}", DncUpdateConsume.class) >> dncUpdateConsume
        customerCacheService.getCustomer("testRmId") >> customerProfile
        retryUpdateDncPhoneService.callUpdateDncPhoneWithRetry(_, _, _, _, _) >> dncUpdateFailProducer

        when: "processing message"
        service.processMessage(message, acknowledgment)

        then: "should call retry service with expiration date"
        1 * retryUpdateDncPhoneService.callUpdateDncPhoneWithRetry(
            headers, 
            "productGroup1", 
            { UpdatePhoneNumbersRequest req -> 
                req.expirationDateTime == "2024-12-31T23:59Z"
            }, 
            _, 
            _
        )
    }

    def "should build UpdatePhoneNumbersRequest without expiration date for default values"() {
        given: "DNC update with default expiration date"
        def message = '{"headers": "{}", "dncUpdateConsumer": "{}"}'
        def headers = new HttpHeaders()
        def dncUpdateConsume = createMockDncUpdateConsumeWithDefaultExpiration()
        def customerProfile = createMockCustomerProfile()
        def dncUpdateFailProducer = createMockDncUpdateFailProducer()

        and: "mock setup"
        objectMapper.readValue(message, _ as TypeReference) >> ["headers": "{}", "dncUpdateConsumer": "{}"]
        objectMapper.readValue("{}", HttpHeaders.class) >> headers
        objectMapper.readValue("{}", DncUpdateConsume.class) >> dncUpdateConsume
        customerCacheService.getCustomer("testRmId") >> customerProfile
        retryUpdateDncPhoneService.callUpdateDncPhoneWithRetry(_, _, _, _, _) >> dncUpdateFailProducer

        when: "processing message"
        service.processMessage(message, acknowledgment)

        then: "should call retry service without expiration date"
        1 * retryUpdateDncPhoneService.callUpdateDncPhoneWithRetry(
            headers, 
            "productGroup1", 
            { UpdatePhoneNumbersRequest req -> 
                req.expirationDateTime == null
            }, 
            _, 
            _
        )
    }

    def "should aggregate multiple DNC update failures"() {
        given: "message with multiple DNC updates"
        def message = '{"headers": "{}", "dncUpdateConsumer": "{}"}'
        def headers = new HttpHeaders()
        def dncUpdateConsume = createMockDncUpdateConsumeMultiple()
        def customerProfile = createMockCustomerProfile()
        def dncUpdateFailProducer1 = createMockDncUpdateFailProducer()
        def dncUpdateFailProducer2 = createMockDncUpdateFailProducer()

        and: "mock setup"
        objectMapper.readValue(message, _ as TypeReference) >> ["headers": "{}", "dncUpdateConsumer": "{}"]
        objectMapper.readValue("{}", HttpHeaders.class) >> headers
        objectMapper.readValue("{}", DncUpdateConsume.class) >> dncUpdateConsume
        customerCacheService.getCustomer("testRmId") >> customerProfile
        retryUpdateDncPhoneService.callUpdateDncPhoneWithRetry(_, "productGroup1", _, _, _) >> dncUpdateFailProducer1
        retryUpdateDncPhoneService.callUpdateDncPhoneWithRetry(_, "productGroup2", _, _, _) >> dncUpdateFailProducer2

        when: "processing message"
        service.processMessage(message, acknowledgment)

        then: "should call retry service for each product group"
        1 * retryUpdateDncPhoneService.callUpdateDncPhoneWithRetry(_, "productGroup1", _, _, _)
        1 * retryUpdateDncPhoneService.callUpdateDncPhoneWithRetry(_, "productGroup2", _, _, _)
        1 * dncPhoneUpdateFailTransactionInsertService.insertTransactionDncPhoneUpdateFail(_, _, _, _)
    }

    // Helper methods to create mock objects
    private DncUpdateConsume createMockDncUpdateConsume() {
        return DncUpdateConsume.builder()
            .profile(DncUpdateConsume.Profile.builder()
                .rmId("testRmId")
                .ecId("testEcId")
                .build())
            .body([DncUpdateConsume.Body.builder()
                .productGroupId("productGroup1")
                .dncList(DncUpdateConsume.DncList.builder()
                    .action("ADD")
                    .expirationDateTime("2024-12-31T23:59Z")
                    .build())
                .build()])
            .build()
    }

    private DncUpdateConsume createMockDncUpdateConsumeWithEcId() {
        return DncUpdateConsume.builder()
            .profile(DncUpdateConsume.Profile.builder()
                .rmId(null)
                .ecId("testEcId")
                .build())
            .body([DncUpdateConsume.Body.builder()
                .productGroupId("productGroup1")
                .dncList(DncUpdateConsume.DncList.builder()
                    .action("ADD")
                    .build())
                .build()])
            .build()
    }

    private DncUpdateConsume createMockDncUpdateConsumeWithExpiration() {
        return DncUpdateConsume.builder()
            .profile(DncUpdateConsume.Profile.builder()
                .rmId("testRmId")
                .build())
            .body([DncUpdateConsume.Body.builder()
                .productGroupId("productGroup1")
                .dncList(DncUpdateConsume.DncList.builder()
                    .action("ADD")
                    .expirationDateTime("2024-12-31T23:59Z")
                    .build())
                .build()])
            .build()
    }

    private DncUpdateConsume createMockDncUpdateConsumeWithDefaultExpiration() {
        return DncUpdateConsume.builder()
            .profile(DncUpdateConsume.Profile.builder()
                .rmId("testRmId")
                .build())
            .body([DncUpdateConsume.Body.builder()
                .productGroupId("productGroup1")
                .dncList(DncUpdateConsume.DncList.builder()
                    .action("ADD")
                    .expirationDateTime("4000-12-31T23:59Z")
                    .build())
                .build()])
            .build()
    }

    private DncUpdateConsume createMockDncUpdateConsumeMultiple() {
        return DncUpdateConsume.builder()
            .profile(DncUpdateConsume.Profile.builder()
                .rmId("testRmId")
                .build())
            .body([
                DncUpdateConsume.Body.builder()
                    .productGroupId("productGroup1")
                    .dncList(DncUpdateConsume.DncList.builder()
                        .action("ADD")
                        .build())
                    .build(),
                DncUpdateConsume.Body.builder()
                    .productGroupId("productGroup2")
                    .dncList(DncUpdateConsume.DncList.builder()
                        .action("REMOVE")
                        .build())
                    .build()
            ])
            .build()
    }

    private CustomerProfileResponse createMockCustomerProfile() {
        return CustomerProfileResponse.builder()
            .customer(CustomerProfileResponse.Customer.builder()
                .profile(CustomerProfileResponse.Profile.builder()
                    .rmId("testRmId")
                    .ccId("testCcId")
                    .build())
                .phones([
                    CustomerProfileResponse.Phone.builder()
                        .phoneType("M")
                        .phoneNo("0812345678")
                        .build()
                ])
                .build())
            .build()
    }

    private CustomerProfileResponse createCustomerProfileWithPhones() {
        return CustomerProfileResponse.builder()
            .customer(CustomerProfileResponse.Customer.builder()
                .phones([
                    CustomerProfileResponse.Phone.builder().phoneType("M").phoneNo("0812345678").build(),
                    CustomerProfileResponse.Phone.builder().phoneType("B").phoneNo("0823456789").build(),
                    CustomerProfileResponse.Phone.builder().phoneType("R").phoneNo("0834567890").build(),
                    CustomerProfileResponse.Phone.builder().phoneType("H").phoneNo("0845678901").build()
                ])
                .build())
            .build()
    }

    private CustomerProfileResponse createCustomerProfileWithNullPhones() {
        return CustomerProfileResponse.builder()
            .customer(CustomerProfileResponse.Customer.builder()
                .phones([
                    CustomerProfileResponse.Phone.builder().phoneType("M").phoneNo("0812345678").build(),
                    CustomerProfileResponse.Phone.builder().phoneType("M").phoneNo(null).build(),
                    null
                ])
                .build())
            .build()
    }

    private ResponseModel<CustomerProfileResponse> createMockResponseModel(CustomerProfileResponse data, String code) {
        return ResponseModel.<CustomerProfileResponse>builder()
            .code(code)
            .header("Test Header")
            .status(ResponseModel.Status.builder()
                .description("Test Status")
                .build())
            .dataObj(data)
            .build()
    }

    private DncUpdateFailProducer createMockDncUpdateFailProducer() {
        return DncUpdateFailProducer.builder()
            .body([])
            .errors([])
            .build()
    }

    def "should verify field annotations and modifiers"() {
        when: "checking field annotations"
        def objectMapperField = DncUpdateConsumerServiceImpl.class.getDeclaredField("objectMapper")
        def customerDataServiceField = DncUpdateConsumerServiceImpl.class.getDeclaredField("customerDataServiceFeignClient")
        def customerCacheServiceField = DncUpdateConsumerServiceImpl.class.getDeclaredField("customerCacheService")
        def insertServiceField = DncUpdateConsumerServiceImpl.class.getDeclaredField("dncPhoneUpdateFailTransactionInsertService")
        def retryServiceField = DncUpdateConsumerServiceImpl.class.getDeclaredField("retryUpdateDncPhoneService")
        def producerField = DncUpdateConsumerServiceImpl.class.getDeclaredField("producer")
        def customerProfileResponseField = DncUpdateConsumerServiceImpl.class.getDeclaredField("customerProfileResponse")

        then: "fields should have correct modifiers"
        java.lang.reflect.Modifier.isFinal(objectMapperField.modifiers)
        java.lang.reflect.Modifier.isFinal(customerDataServiceField.modifiers)
        java.lang.reflect.Modifier.isFinal(customerCacheServiceField.modifiers)
        java.lang.reflect.Modifier.isFinal(insertServiceField.modifiers)
        java.lang.reflect.Modifier.isFinal(retryServiceField.modifiers)
        java.lang.reflect.Modifier.isFinal(producerField.modifiers)
        !java.lang.reflect.Modifier.isFinal(customerProfileResponseField.modifiers)
    }

    def "should handle empty phone list"() {
        given: "customer profile with empty phone list"
        def customerProfile = CustomerProfileResponse.builder()
            .customer(CustomerProfileResponse.Customer.builder()
                .phones([])
                .build())
            .build()

        when: "calling listPhoneNumber method"
        def result = service.listPhoneNumber(customerProfile)

        then: "should return empty array"
        result.length == 0
    }

    def "should handle customer profile with null phones list"() {
        given: "customer profile with null phones"
        def customerProfile = CustomerProfileResponse.builder()
            .customer(CustomerProfileResponse.Customer.builder()
                .phones(null)
                .build())
            .build()

        when: "calling listPhoneNumber method"
        service.listPhoneNumber(customerProfile)

        then: "should throw NullPointerException"
        thrown(NullPointerException)
    }

    def "should log success message"() {
        given: "valid message processing"
        def message = '{"headers": "{}", "dncUpdateConsumer": "{}"}'
        def headers = new HttpHeaders()
        def dncUpdateConsume = createMockDncUpdateConsume()
        def customerProfile = createMockCustomerProfile()
        def dncUpdateFailProducer = createMockDncUpdateFailProducer()

        and: "mock setup"
        objectMapper.readValue(message, _ as TypeReference) >> ["headers": "{}", "dncUpdateConsumer": "{}"]
        objectMapper.readValue("{}", HttpHeaders.class) >> headers
        objectMapper.readValue("{}", DncUpdateConsume.class) >> dncUpdateConsume
        customerCacheService.getCustomer("testRmId") >> customerProfile
        retryUpdateDncPhoneService.callUpdateDncPhoneWithRetry(_, _, _, _, _) >> dncUpdateFailProducer

        when: "processing message"
        service.processMessage(message, acknowledgment)

        then: "should complete successfully and log success"
        noExceptionThrown()
        // Note: In real implementation, you might want to capture logs using a test appender
    }

    def "should handle null expiration date time"() {
        given: "DNC update with null expiration date"
        def message = '{"headers": "{}", "dncUpdateConsumer": "{}"}'
        def headers = new HttpHeaders()
        def dncUpdateConsume = DncUpdateConsume.builder()
            .profile(DncUpdateConsume.Profile.builder()
                .rmId("testRmId")
                .build())
            .body([DncUpdateConsume.Body.builder()
                .productGroupId("productGroup1")
                .dncList(DncUpdateConsume.DncList.builder()
                    .action("ADD")
                    .expirationDateTime(null)
                    .build())
                .build()])
            .build()
        def customerProfile = createMockCustomerProfile()
        def dncUpdateFailProducer = createMockDncUpdateFailProducer()

        and: "mock setup"
        objectMapper.readValue(message, _ as TypeReference) >> ["headers": "{}", "dncUpdateConsumer": "{}"]
        objectMapper.readValue("{}", HttpHeaders.class) >> headers
        objectMapper.readValue("{}", DncUpdateConsume.class) >> dncUpdateConsume
        customerCacheService.getCustomer("testRmId") >> customerProfile
        retryUpdateDncPhoneService.callUpdateDncPhoneWithRetry(_, _, _, _, _) >> dncUpdateFailProducer

        when: "processing message"
        service.processMessage(message, acknowledgment)

        then: "should build request without expiration date"
        1 * retryUpdateDncPhoneService.callUpdateDncPhoneWithRetry(
            headers,
            "productGroup1",
            { UpdatePhoneNumbersRequest req ->
                req.expirationDateTime == null
            },
            _,
            _
        )
    }

    def "should transform DncUpdateFailProducer to DncPhoneUpdateFailTransaction correctly"() {
        given: "DncUpdateFailProducer with data"
        def message = '{"headers": "{}", "dncUpdateConsumer": "{}"}'
        def headers = new HttpHeaders()
        def dncUpdateConsume = createMockDncUpdateConsume()
        def customerProfile = createMockCustomerProfile()
        def dncUpdateFailProducer = DncUpdateFailProducer.builder()
            .body([DncUpdateFailProducer.DncUpdatePhone.builder()
                .dncListId("testListId")
                .dncList(DncUpdateFailProducer.DncList.builder()
                    .action("ADD")
                    .phoneNumbers(["0812345678"])
                    .expirationDateTime("2024-12-31T23:59Z")
                    .build())
                .build()])
            .errors([DncUpdateFailProducer.DncUpdateError.builder()
                .httpStatus("500")
                .code("ERR001")
                .message("Test error")
                .status("FAILED")
                .build()])
            .build()

        and: "mock setup"
        objectMapper.readValue(message, _ as TypeReference) >> ["headers": "{}", "dncUpdateConsumer": "{}"]
        objectMapper.readValue("{}", HttpHeaders.class) >> headers
        objectMapper.readValue("{}", DncUpdateConsume.class) >> dncUpdateConsume
        customerCacheService.getCustomer("testRmId") >> customerProfile
        retryUpdateDncPhoneService.callUpdateDncPhoneWithRetry(_, _, _, _, _) >> dncUpdateFailProducer

        when: "processing message"
        service.processMessage(message, acknowledgment)

        then: "should transform and insert transaction correctly"
        1 * dncPhoneUpdateFailTransactionInsertService.insertTransactionDncPhoneUpdateFail(
            headers,
            { DncPhoneUpdateFailTransaction transaction ->
                transaction.dncBody.size() == 1 &&
                transaction.dncBody[0].dncListId == "testListId" &&
                transaction.dncError.size() == 1 &&
                transaction.dncError[0].serviceName == Constants.POST_DNCLIST_PHONENUMBER_ENDPOINT &&
                transaction.dncError[0].httpStatus == "500"
            },
            "testRmId",
            "testEcId"
        )
    }
}
