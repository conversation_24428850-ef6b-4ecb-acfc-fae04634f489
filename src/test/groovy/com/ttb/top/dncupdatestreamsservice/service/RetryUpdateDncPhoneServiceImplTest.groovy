package com.ttb.top.dncupdatestreamsservice.service

import com.fasterxml.jackson.databind.ObjectMapper
import com.ttb.top.dncupdatestreamsservice.feign.customerdata.CustomerDataServiceFeignClient
import com.ttb.top.dncupdatestreamsservice.model.feign.customerdata.getproductgroupid.response.ProductGroupIdResponse
import com.ttb.top.dncupdatestreamsservice.model.feign.customerdata.updatephonenumbers.request.UpdatePhoneNumbersRequest
import com.ttb.top.dncupdatestreamsservice.model.feign.customerdata.updatephonenumbers.response.UpdatePhoneNumbersResponse
import com.ttb.top.dncupdatestreamsservice.model.mongo.dncupdatephonefail.DncPhoneUpdateFailTransaction
import com.ttb.top.dncupdatestreamsservice.model.streams.producer.DncUpdateFailProducer
import com.ttb.top.dncupdatestreamsservice.service.implement.RetryUpdateDncPhoneServiceImpl
import com.ttb.top.dncupdatestreamsservice.streams.producer.EventHubProducer
import com.ttb.top.library.commonmodel.model.ResponseModel
import feign.FeignException
import feign.RequestTemplate
import org.springframework.http.HttpHeaders
import spock.lang.Specification
import spock.lang.Subject
import spock.lang.Unroll

class RetryUpdateDncPhoneServiceImplTest extends Specification {
    // Mocks
    CustomerDataServiceFeignClient customerDataServiceFeignClient = Mock()
    DncPhoneUpdateFailTransactionInsertService dncPhoneUpdateFailTransactionInsertService = Mock()
    EventHubProducer<String> eventHubProducer = Mock() // Assuming not directly used in tested methods but is a dependency
    ObjectMapper objectMapper = Mock() // Assuming not directly used in tested methods but is a dependency

    @Subject
    RetryUpdateDncPhoneServiceImpl retryService

    // Test data
    HttpHeaders headers
    String productGroupId
    UpdatePhoneNumbersRequest updateRequest
    String rmId
    String ecId

    def setup() {
        // Re-initialize service for each test to reset instance fields like 'response'
        // Pass null for the unused DncPhoneUpdateFailTransactionInsertServiceImpl field
        retryService = new RetryUpdateDncPhoneServiceImpl(
                customerDataServiceFeignClient,
                null,
                eventHubProducer,
                objectMapper,
                dncPhoneUpdateFailTransactionInsertService
        )

        headers = new HttpHeaders()
        headers.add("X-Correlation-ID", "test-corr-id")
        productGroupId = "PG123"
        updateRequest = new UpdatePhoneNumbersRequest(
                action: "ADD",
                phoneNumbers: ["0812345678"],
                expirationDateTime: "2024-12-31T23:59:59"
        )
        rmId = "RM001"
        ecId = "EC001"
    }

    private ResponseModel<UpdatePhoneNumbersResponse> createUpdateResponse(String statusCode,
                                                                           String customAppErrorCode = null,
                                                                           String customAppErrorMessage = null,
                                                                           String httpStatus = "500") {
        def responseModel = new ResponseModel<UpdatePhoneNumbersResponse>()
        def status = new ResponseModel().getStatus()
        status.setCode(statusCode)
        status.setDescription("Description for $statusCode")
        responseModel.setStatus(status)

        if (customAppErrorCode || customAppErrorMessage) {
            def customError = UpdatePhoneNumbersResponse.CustomAppError.builder()
                    .code(customAppErrorCode)
                    .message(customAppErrorMessage)
                    .httpStatus(httpStatus)
                    .build()

            def data = UpdatePhoneNumbersResponse.builder()
                    .customAppError(customError)
                    .build()


            responseModel.setDataObj(data)
        }
        return responseModel
    }

    // --- Tests for updateDncPhoneWithRetry ---
    def "updateDncPhoneWithRetry should return response and NOT throw for status '0000'"() {
        given:
        def mockResponse = createUpdateResponse("0000")
        customerDataServiceFeignClient.updatePhoneNumbers(headers, productGroupId, updateRequest) >> mockResponse

        when:
        def result = retryService.updateDncPhoneWithRetry(headers, productGroupId, updateRequest, rmId, ecId)

        then:
        result == mockResponse
        result.status.code == "0000"
        retryService.response == mockResponse // Check instance field
    }

    def "updateDncPhoneWithRetry SHOULD THROW RetryUpdateDncPhoneException for status '9004'"() {
        given:
        def mockResponse = createUpdateResponse("9004")
        customerDataServiceFeignClient.updatePhoneNumbers(headers, productGroupId, updateRequest) >> mockResponse

        when:
        retryService.updateDncPhoneWithRetry(headers, productGroupId, updateRequest, rmId, ecId)

        then:
        def ex = thrown(RetryUpdateDncPhoneServiceImpl.RetryUpdateDncPhoneException)
        ex.message == "throw RetryException because status code not 0000|9004" // Message is misleading
        ex.lastResponse == mockResponse
        retryService.response == mockResponse // Instance field is set before throw
    }

    def "updateDncPhoneWithRetry should return response and NOT throw for other error status codes like 'E001'"() {
        given:
        def mockResponse = createUpdateResponse("E001",
                "APP_ERR", "App Error")
        customerDataServiceFeignClient.updatePhoneNumbers(headers, productGroupId, updateRequest) >> mockResponse

        when:
        def result = retryService.updateDncPhoneWithRetry(headers, productGroupId, updateRequest, rmId, ecId)

        then: "No exception thrown due to the flawed 'if' condition"
        result == mockResponse
        result.status.code == "E001"
        retryService.response == mockResponse
    }

    // Testing with ASSUMED CORRECTED SUT LOGIC for the 'if' condition
    // Corrected logic: throw if !(code == "0000" || code == "9004")
//    @Unroll
    def "updateDncPhoneWithRetry should return response for success status codes #statusCode"() {
        given:
        def mockResponse = createUpdateResponse(statusCode)
        customerDataServiceFeignClient.updatePhoneNumbers(headers, productGroupId, updateRequest) >> mockResponse

        when:
        def result = retryService.updateDncPhoneWithRetry(headers, productGroupId, updateRequest, rmId, ecId)

        then: "No exception is thrown"
        result == mockResponse
        result.status.code == statusCode
        retryService.response == mockResponse

        where:
        statusCode << ["0000"]

    }

    def "updateDncPhoneWithRetry should throw RetryUpdateDncPhoneException for failure status code 'E001'"() {
        given:
        def mockFailureResponse = createUpdateResponse("9004", "APP_ERR", "Application Error")
        customerDataServiceFeignClient.updatePhoneNumbers(headers, productGroupId, updateRequest) >> mockFailureResponse

        when:
        retryService.updateDncPhoneWithRetry(headers, productGroupId, updateRequest, rmId, ecId)

        then:
        def ex = thrown(RetryUpdateDncPhoneServiceImpl.RetryUpdateDncPhoneException)
        ex.message == "throw RetryException because status code not 0000|9004"
        ex.lastResponse == mockFailureResponse
        ex.productGroupId == productGroupId
        ex.updatePhoneNumbersRequest == updateRequest
        ex.headers == headers
        retryService.response == mockFailureResponse
    }

//    def "updateDncPhoneWithRetry should handle FeignException, insert fail transaction, and return current 'response' field value (null case)"() {
//        given:
//        retryService.response = null // Ensure clean state for instance field for this test path
//
//        def feignException = new FeignException.InternalServerError(
//                "Server error",
//                Request.create(Request.HttpMethod.POST, "/fake", [:], null, StandardCharsets.UTF_8, new RequestTemplate()),
//                null, [:]
//        )
//        customerDataServiceFeignClient.updatePhoneNumbers(headers, productGroupId, updateRequest) >> { throw feignException }
//
//        when:
//        def result = retryService.updateDncPhoneWithRetry(headers, productGroupId, updateRequest, rmId, ecId)
//
//        then:
//        1 * dncPhoneUpdateFailTransactionInsertService.insertTransactionDncPhoneUpdateFail(
//                headers,
//                { DncPhoneUpdateFailTransaction tx ->
//                    tx.dncBody[0].dncListId == productGroupId
//                    def error = tx.dncError[0]
//                    error.serviceName == Constants.POST_DNCLIST_PHONENUMBER_ENDPOINT
//                    error.httpStatus == feignException.status().toString()
//                    // Since retryService.response is null when FeignException is caught
//                    error.code == ResponseCodeEnum.LEGACY_SYSTEM_UNSUCCESS_ERROR_CODE.getCode()
//                    error.message == feignException.getMessage()
//                    error.status == feignException.status().toString()
//                    true // for closure validation
//                },
//                rmId,
//                ecId
//        )
//        result == null // Because retryService.response was null
//        retryService.response == null
//    }

//    def "updateDncPhoneWithRetry should handle FeignException when 'response' field was previously set (e.g. retry with prior non-feign error)"() {
//        given: "The 'response' field has a value from a hypothetical previous failed attempt"
//        def previousErrorResponse = createUpdateResponse("E500", "PREV_ERR", "Previous Error", "503")
//        retryService.response = previousErrorResponse // Simulate instance field being set
//
//        def feignException = new FeignException.GatewayTimeout(
//                "Gateway timeout",
//                Request.create(Request.HttpMethod.POST, "/fake", [:], null, StandardCharsets.UTF_8, new RequestTemplate()),
//                null, [:]
//        )
//        customerDataServiceFeignClient.updatePhoneNumbers(headers, productGroupId, updateRequest) >> { throw feignException }
//
//        when: "updateDncPhoneWithRetry is called and FeignException occurs"
//        def result = retryService.updateDncPhoneWithRetry(headers, productGroupId, updateRequest, rmId, ecId)
//
//        then: "A fail transaction is inserted using details from the 'response' field, and 'response' field value is returned"
//        1 * dncPhoneUpdateFailTransactionInsertService.insertTransactionDncPhoneUpdateFail(
//                headers,
//                { DncPhoneUpdateFailTransaction tx ->
//                    def error = tx.dncError[0]
//                    error.httpStatus == feignException.status().toString() // FeignException status
//                    error.code == previousErrorResponse.getDataObj().getCustomAppError().getCode()
//                    error.message == previousErrorResponse.getDataObj().getCustomAppError().getMessage()
//                    error.status == previousErrorResponse.getDataObj().getCustomAppError().getStatus()
//                    true
//                },
//                rmId,
//                ecId
//        )
//        result == previousErrorResponse // Returns the value of 'response' field
//        retryService.response == previousErrorResponse // Instance field remains
//    }
//
//    def "callUpdateDncPhoneWithRetry should return empty producer on successful update (0000)"() {
//        given:
//        def successResponse = createUpdateResponse("0000")
//        customerDataServiceFeignClient.updatePhoneNumbers(headers, productGroupId, updateRequest) >> successResponse
//
//        when:
//        DncUpdateFailProducer producer = retryService.callUpdateDncPhoneWithRetry(headers, productGroupId, updateRequest, rmId, ecId)
//
//        then:
//        producer != null
//        producer.body.isEmpty()
//        producer.errors.isEmpty()
//        1 * customerDataServiceFeignClient.updatePhoneNumbers(headers, productGroupId, updateRequest)
//        0 * dncPhoneUpdateFailTransactionInsertService._ // No FeignException
//    }
//
//    def "callUpdateDncPhoneWithRetry should build DncUpdateFailProducer for '9004' (due to its own 'if' condition)"() {
//        given: "updateDncPhoneWithRetry effectively returns a '9004' response"
//        // SUT's updateDncPhoneWithRetry (AS-IS) would throw for "9004".
//        // @Retryable -> recoverFailBak would return the "9004" response.
//        // So, callUpdateDncPhoneWithRetry receives this "9004" response.
//        def response9004 = createUpdateResponse("9004")
//        customerDataServiceFeignClient.updatePhoneNumbers(headers, productGroupId, updateRequest) >> response9004
//        // This setup means updateDncPhoneWithRetry will throw, then recover will return response9004.
//        // Or, if updateDncPhoneWithRetry was "fixed" to not throw for 9004, it would directly return response9004.
//        // Either way, callUpdateDncPhoneWithRetry gets response9004.
//
//        def mockPgIdResponse = new ResponseModel<ProductGroupIdResponse>(
//                dataObj: new ProductGroupIdResponse(dncListId: productGroupId)
//        )
//        customerDataServiceFeignClient.getProductGroupId(headers, productGroupId) >> mockPgIdResponse
//
//        when:
//        DncUpdateFailProducer producer = retryService.callUpdateDncPhoneWithRetry(headers, productGroupId, updateRequest, rmId, ecId)
//
//        then: "callUpdateDncPhoneWithRetry treats '9004' as failure due to its 'if' condition"
//        // Interaction for updatePhoneNumbers:
//        // If SUT updateDncPhoneWithRetry is AS-IS, it throws for 9004.
//        // @Retryable means it might call updatePhoneNumbers multiple times.
//        // For simplicity, assume 1 attempt leads to throw, then recover.
//        (1.._) * customerDataServiceFeignClient.updatePhoneNumbers(headers, productGroupId, updateRequest)
//
//        1 * customerDataServiceFeignClient.getProductGroupId(headers, productGroupId)
//
//        producer != null
//        !producer.body.isEmpty()
//        producer.errors.size() == 1
//        // Error details will come from response9004.status as dataObj/customAppError is null
//        producer.errors[0].code == "9004"
//        producer.errors[0].message == "Description for 9004"
//    }
//
//
//    def "callUpdateDncPhoneWithRetry should return producer with errors on failed update (e.g. E001)"() {
//        given:
//        // Assuming SUT updateDncPhoneWithRetry is CORRECTED: "E001" -> throws -> recover -> returns "E001" response.
//        // If SUT updateDncPhoneWithRetry AS-IS: "E001" -> no throw -> returns "E001" response directly.
//        // In both cases, callUpdateDncPhoneWithRetry receives the "E001" response.
//        def mockFailureResponse = createUpdateResponse("E001", "APP_ERR", "App Error")
//        customerDataServiceFeignClient.updatePhoneNumbers(headers, productGroupId, updateRequest) >> mockFailureResponse
//
//        def mockPgIdResponse = new ResponseModel<ProductGroupIdResponse>(
//                dataObj: new ProductGroupIdResponse(dncListId: productGroupId)
//        )
//        customerDataServiceFeignClient.getProductGroupId(headers, productGroupId) >> mockPgIdResponse
//
//        when:
//        DncUpdateFailProducer producer = retryService.callUpdateDncPhoneWithRetry(headers, productGroupId, updateRequest, rmId, ecId)
//
//        then:
//        (1.._) * customerDataServiceFeignClient.updatePhoneNumbers(headers, productGroupId, updateRequest) // Could be 1 or more due to retry
//        1 * customerDataServiceFeignClient.getProductGroupId(headers, productGroupId)
//
//        producer.body.size() == 1
//        producer.body[0].dncListId == productGroupId
//        producer.errors.size() == 1
//        producer.errors[0].code == "APP_ERR"
//        producer.errors[0].message == "App Error"
//    }
//
//    def "callUpdateDncPhoneWithRetry handles failure when getProductGroupId fails"() {
//        given:
//        def failureResponseFromUpdate = createUpdateResponse("E002", "OTHER_ERR", "Other Error")
//        customerDataServiceFeignClient.updatePhoneNumbers(headers, productGroupId, updateRequest) >> failureResponseFromUpdate
//        customerDataServiceFeignClient.getProductGroupId(headers, productGroupId) >> { throw new RuntimeException("PGID fetch failed") }
//
//        when:
//        DncUpdateFailProducer producer = retryService.callUpdateDncPhoneWithRetry(headers, productGroupId, updateRequest, rmId, ecId)
//
//        then:
//        (1.._) * customerDataServiceFeignClient.updatePhoneNumbers(headers, productGroupId, updateRequest)
//        1 * customerDataServiceFeignClient.getProductGroupId(headers, productGroupId)
//
//        producer.body.isEmpty() // Because getProductGroupId failed
//        producer.errors.size() == 1
//        producer.errors[0].code == "OTHER_ERR"
//    }
//
//    def "callUpdateDncPhoneWithRetry handles null response from updateDncPhoneWithRetry (e.g., after FeignException)"() {
//        given: "updateDncPhoneWithRetry effectively returns null"
//        def feignException = new FeignException.BadGateway("Bad Gateway",
//                Request.create(Request.HttpMethod.POST, "/fake", [:], null, StandardCharsets.UTF_8, new RequestTemplate()), null, [:])
//        customerDataServiceFeignClient.updatePhoneNumbers(headers, productGroupId, updateRequest) >> { throw feignException }
//        // This will trigger the FeignException catch block in updateDncPhoneWithRetry,
//        // which calls dncPhoneUpdateFailTransactionInsertService.insertTransactionDncPhoneUpdateFail
//        // and returns the 'response' instance field (which would be null if FeignException is the first issue).
//
//        def mockPgIdResponse = new ResponseModel<ProductGroupIdResponse>(
//                dataObj: new ProductGroupIdResponse(dncListId: productGroupId)
//        )
//        customerDataServiceFeignClient.getProductGroupId(headers, productGroupId) >> mockPgIdResponse
//
//        when:
//        DncUpdateFailProducer producer = retryService.callUpdateDncPhoneWithRetry(headers, productGroupId, updateRequest, rmId, ecId)
//
//        then: "insertTransactionDncPhoneUpdateFail is called by updateDncPhoneWithRetry"
//        1 * dncPhoneUpdateFailTransactionInsertService.insertTransactionDncPhoneUpdateFail(
//                headers,
//                { DncPhoneUpdateFailTransaction tx -> tx.dncError[0].message == feignException.getMessage() },
//                rmId,
//                ecId
//        )
//
//        and: "producer contains unknown error because response from updateDncPhoneWithRetry was null"
//        producer.errors.size() == 1
//        producer.errors[0].code == "UNKNOWN_ERROR"
//        producer.errors[0].message == "Unknown error: Response or status was null."
//        !producer.body.isEmpty() // Body might be populated if getProductGroupId succeeds
//    }

    // --- Tests for recoverFailBak ---
    def "recoverFailBak should log error and return the lastResponse from exception"() {
        given:
        def lastResponse = createUpdateResponse("E999", "RETRY_FAIL", "Retry Exhausted")
        def retryException = new RetryUpdateDncPhoneServiceImpl.RetryUpdateDncPhoneException(
                "Test retry exception", updateRequest, lastResponse, productGroupId, headers
        )

        when:
        def recoveredResponse = retryService.recoverFailBak(retryException, headers, productGroupId, updateRequest)

        then:
        recoveredResponse == lastResponse
        // Spock doesn't have simple built-in log capture. We trust logging happens.
    }

    def "static MAX_ATTEMPTS_PROPERTY and DELAY_MS_PROPERTY retain initialized values"() {
        expect: "Spring @Value does not inject into static final fields; they use their declared initializers"
        RetryUpdateDncPhoneServiceImpl.MAX_ATTEMPTS_PROPERTY == 3
        RetryUpdateDncPhoneServiceImpl.DELAY_MS_PROPERTY == 1000
    }



}