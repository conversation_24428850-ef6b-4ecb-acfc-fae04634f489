package com.ttb.top.dncupdatestreamsservice.service

import com.fasterxml.jackson.databind.ObjectMapper
import com.ttb.top.dncupdatestreamsservice.feign.customerdata.CustomerDataServiceFeignClient
import com.ttb.top.dncupdatestreamsservice.model.feign.customerdata.getproductgroupid.response.ProductGroupIdResponse
import com.ttb.top.dncupdatestreamsservice.model.feign.customerdata.updatephonenumbers.request.UpdatePhoneNumbersRequest
import com.ttb.top.dncupdatestreamsservice.model.feign.customerdata.updatephonenumbers.response.UpdatePhoneNumbersResponse
import com.ttb.top.dncupdatestreamsservice.model.mongo.dncupdatephonefail.DncPhoneUpdateFailTransaction

import com.ttb.top.dncupdatestreamsservice.service.implement.DncPhoneUpdateFailTransactionInsertServiceImpl
import com.ttb.top.dncupdatestreamsservice.service.implement.RetryUpdateDncPhoneServiceImpl
import com.ttb.top.dncupdatestreamsservice.streams.producer.EventHubProducer
import com.ttb.top.library.commonmodel.model.ResponseModel
import com.ttb.top.library.commonmodel.model.ResponseStatus
import feign.FeignException
import feign.Request
import org.springframework.http.HttpHeaders
import spock.lang.Specification
import spock.lang.Subject

import java.nio.charset.StandardCharsets

class RetryUpdateDncPhoneServiceImplTest extends Specification {
    // Mock dependencies
    def customerDataServiceFeignClient = Mock(CustomerDataServiceFeignClient)
    def phoneUpdateFailTransactionInsertService = Mock(DncPhoneUpdateFailTransactionInsertServiceImpl)
    def eventHubProducer = Mock(EventHubProducer)
    def objectMapper = Mock(ObjectMapper)
    def dncPhoneUpdateFailTransactionInsertService = Mock(DncPhoneUpdateFailTransactionInsertService)
    
    // Test data
    def headers = new HttpHeaders()
    def productGroupId = "PG12345"
    def updateRequest = new UpdatePhoneNumbersRequest(
        action: "ADD",
        phoneNumbers: ["0812345678"],
        expirationDateTime: "2023-12-31T23:59:59"
    )
    def rmId = "RM12345"
    def ecId = "EC12345"
    
    @Subject
    RetryUpdateDncPhoneServiceImpl service
    RetryUpdateDncPhoneServiceImpl serviceSpy

    def setup() {
        service = new RetryUpdateDncPhoneServiceImpl(
            customerDataServiceFeignClient,
            phoneUpdateFailTransactionInsertService,
            eventHubProducer,
            objectMapper,
            dncPhoneUpdateFailTransactionInsertService
        )

        serviceSpy = Spy(RetryUpdateDncPhoneServiceImpl, constructorArgs: [
                customerDataServiceFeignClient,
                phoneUpdateFailTransactionInsertService,
                eventHubProducer,
                objectMapper,
                dncPhoneUpdateFailTransactionInsertService
        ])


        headers.add("X-Correlation-ID", "test-correlation-id")
    }
    
    def "updateDncPhoneWithRetry should return success response when status code is 0000"() {
        given: "a successful response from the client"
        def successResponse = createSuccessResponse()
        customerDataServiceFeignClient.updatePhoneNumbers(headers, productGroupId, updateRequest) >> successResponse
        
        when: "the retry method is called"
        def result = service.updateDncPhoneWithRetry(headers, productGroupId, updateRequest, rmId, ecId)
        
        then: "the result should be the success response"
        result == successResponse
        result.status.code == "0000"
    }
    
    def "updateDncPhoneWithRetry should throw RetryUpdateDncPhoneException when status code is 9004"() {
        given: "a response with status code 9004"
        def errorResponse = createErrorResponse("9004", "APP_ERR", "Application Error")

        customerDataServiceFeignClient.updatePhoneNumbers(headers, productGroupId, updateRequest) >> errorResponse
        
        when: "the retry method is called"
        service.updateDncPhoneWithRetry(headers, productGroupId, updateRequest, rmId, ecId)
        
        then: "a RetryUpdateDncPhoneException should be thrown"
        def exception = thrown(RetryUpdateDncPhoneServiceImpl.RetryUpdateDncPhoneException)
        exception.message == "throw RetryException because status code not 0000|9004"
        exception.lastResponse == errorResponse
        exception.productGroupId == productGroupId
        exception.updatePhoneNumbersRequest == updateRequest
        exception.headers == headers
    }
    
    def "updateDncPhoneWithRetry should handle FeignException and insert fail transaction"() {
        given: "a FeignException from the client"
        def feignException = Mock(FeignException.FeignServerException)
        customerDataServiceFeignClient.updatePhoneNumbers(headers, productGroupId, updateRequest) >> { throw feignException }
        
        when: "the retry method is called"
        def result = service.updateDncPhoneWithRetry(headers, productGroupId, updateRequest, rmId, ecId)
        
        then: "a fail transaction should be inserted"
        1 * dncPhoneUpdateFailTransactionInsertService.insertTransactionDncPhoneUpdateFail(
            headers,
            { DncPhoneUpdateFailTransaction transaction ->
                transaction.dncBody.size() == 1 &&
                transaction.dncBody[0].dncListId == productGroupId &&
                transaction.dncError.size() == 1
            },
            rmId,
            ecId
        )
        
        and: "the result should be null since no response was set"
        result == null
    }
    
    def "callUpdateDncPhoneWithRetry should return empty producer on successful update"() {
        given: "a successful response from updateDncPhoneWithRetry"
        def successResponse = createSuccessResponse()
        customerDataServiceFeignClient.updatePhoneNumbers(headers, productGroupId, updateRequest) >> successResponse
        
        when: "callUpdateDncPhoneWithRetry is called"
        def result = service.callUpdateDncPhoneWithRetry(headers, productGroupId, updateRequest, rmId, ecId)
        
        then: "an empty DncUpdateFailProducer should be returned"
        result != null
        result.body.isEmpty()
        result.errors.isEmpty()
    }
    
    def "callUpdateDncPhoneWithRetry should build error producer when update fails"() {
        given: "an error response from updateDncPhoneWithRetry"
        def errorResponse = createErrorResponse("E001", "VALIDATION_ERR", "Validation Error")
        serviceSpy.updateDncPhoneWithRetry(_, _, _, _, _) >> errorResponse
        customerDataServiceFeignClient.updatePhoneNumbers(headers, productGroupId, updateRequest) >> errorResponse

        and: "a successful product group ID response"
        def pgIdResponse = new ResponseModel<ProductGroupIdResponse>(
            dataObj: new ProductGroupIdResponse(dncListId: productGroupId),
            status:  new ResponseStatus("9004")
        )

        customerDataServiceFeignClient.getProductGroupId(headers, productGroupId) >> pgIdResponse
        
        when: "callUpdateDncPhoneWithRetry is called"
//        def result = service.callUpdateDncPhoneWithRetry(headers, productGroupId, updateRequest, rmId, ecId)
        def result = serviceSpy.callUpdateDncPhoneWithRetry(headers, productGroupId, updateRequest, rmId, ecId)
        
        then: "a DncUpdateFailProducer with error details should be returned"
        result != null
        !result.body.isEmpty()
        result.body.size() == 1
        result.body[0].dncListId == productGroupId
        !result.errors.isEmpty()
        result.errors.size() == 1
        result.errors[0].code == "E001"
        result.errors[0].message == "Validation Error"
    }
    
    def "recoverFailBak should return the lastResponse from the exception"() {
        given: "a RetryUpdateDncPhoneException with a response"
        def errorResponse = createErrorResponse("E999", "RETRY_FAIL", "Retry Exhausted")
        def exception = new RetryUpdateDncPhoneServiceImpl.RetryUpdateDncPhoneException(
            "Test retry exception", updateRequest, errorResponse, productGroupId, headers
        )
        
        when: "recoverFailBak is called"
        def result = service.recoverFailBak(exception, headers, productGroupId, updateRequest)
        
        then: "the lastResponse from the exception should be returned"
        result == errorResponse
    }
    
    // Helper methods to create test responses
    private ResponseModel<UpdatePhoneNumbersResponse> createSuccessResponse() {
        def response = UpdatePhoneNumbersResponse.builder()
                .customAppError(null)
                .build()

        return new ResponseModel<UpdatePhoneNumbersResponse>(
            dataObj: response,
            status: new ResponseStatus("0000")
        )
    }
    
    private ResponseModel<UpdatePhoneNumbersResponse> createErrorResponse(String code, String status, String message) {
        def customAppError = UpdatePhoneNumbersResponse.CustomAppError.builder()
                .code(code)
                .status(status)
                .message(message)
                .httpStatus("400")
                .build()

        UpdatePhoneNumbersResponse response = UpdatePhoneNumbersResponse.builder()
                .customAppError(customAppError)
                .build()
        
        return new ResponseModel<UpdatePhoneNumbersResponse>(
            dataObj: response,
            status: new ResponseStatus("9004")
        )
    }
}
