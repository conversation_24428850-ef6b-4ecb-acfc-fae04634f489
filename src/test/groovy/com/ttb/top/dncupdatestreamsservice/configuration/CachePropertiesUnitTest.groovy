package com.ttb.top.dncupdatestreamsservice.configuration

import com.fasterxml.jackson.databind.ObjectMapper
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Configuration
import org.springframework.data.redis.cache.RedisCacheConfiguration
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer
import org.springframework.data.redis.serializer.RedisSerializationContext
import org.springframework.data.redis.serializer.StringRedisSerializer
import spock.lang.Specification
import spock.lang.Subject
import spock.lang.Unroll
import java.time.Duration
import java.time.LocalTime
import java.time.format.DateTimeParseException

class CachePropertiesUnitTest extends Specification {

    @Subject
    CacheProperties cacheProperties = new CacheProperties()

    def "should have correct class annotations"() {
        expect: "class should be properly annotated"
        CacheProperties.class.isAnnotationPresent(lombok.Setter.class)
        CacheProperties.class.isAnnotationPresent(Configuration.class)
        CacheProperties.class.isAnnotationPresent(ConfigurationProperties.class)
        
        def configPropertiesAnnotation = CacheProperties.class.getAnnotation(ConfigurationProperties.class)
        configPropertiesAnnotation.prefix() == "cache.customer360"
    }

    def "should initialize with empty maps"() {
        when: "creating new CacheProperties instance"
        def newInstance = new CacheProperties()

        then: "should have empty maps initialized"
        getPrivateField(newInstance, "expireAtTime") != null
        getPrivateField(newInstance, "expireSeconds") != null
        getPrivateField(newInstance, "expireAtTime").isEmpty()
        getPrivateField(newInstance, "expireSeconds").isEmpty()
    }

    def "should return empty map when no configurations are set"() {
        when: "getting initial cache configurations with empty properties"
        def result = cacheProperties.getInitialCacheConfigurations()

        then: "should return empty map"
        result != null
        result instanceof Map
        result.isEmpty()
    }

    def "should create cache configurations from expireSeconds only"() {
        given: "cache properties with only expireSeconds"
        cacheProperties.expireSeconds = [
            "cache1": 1800L,
            "cache2": 3600L
        ]

        when: "getting initial cache configurations"
        def result = cacheProperties.getInitialCacheConfigurations()

        then: "should create configurations for expireSeconds caches"
        result.size() == 2
        result.containsKey("cache1")
        result.containsKey("cache2")
        result["cache1"] instanceof RedisCacheConfiguration
        result["cache2"] instanceof RedisCacheConfiguration
    }

    def "should create cache configurations from expireAtTime only"() {
        given: "cache properties with only expireAtTime"
        cacheProperties.expireAtTime = [
            "morning": "06:00",
            "evening": "18:00"
        ]

        when: "getting initial cache configurations"
        def result = cacheProperties.getInitialCacheConfigurations()

        then: "should create configurations for expireAtTime caches"
        result.size() == 2
        result.containsKey("morning")
        result.containsKey("evening")
        result["morning"] instanceof RedisCacheConfiguration
        result["evening"] instanceof RedisCacheConfiguration
    }

    def "should create cache configurations from both expireSeconds and expireAtTime"() {
        given: "cache properties with both types"
        cacheProperties.expireSeconds = ["ttl-cache": 1800L]
        cacheProperties.expireAtTime = ["time-cache": "12:00"]

        when: "getting initial cache configurations"
        def result = cacheProperties.getInitialCacheConfigurations()

        then: "should create configurations for both types"
        result.size() == 2
        result.containsKey("ttl-cache")
        result.containsKey("time-cache")
        result.values().every { it instanceof RedisCacheConfiguration }
    }

    def "should handle duplicate cache names by overriding"() {
        given: "same cache name in both maps"
        cacheProperties.expireSeconds = ["duplicate": 1800L]
        cacheProperties.expireAtTime = ["duplicate": "12:00"]

        when: "getting initial cache configurations"
        def result = cacheProperties.getInitialCacheConfigurations()

        then: "should have only one entry (expireAtTime processed after expireSeconds)"
        result.size() == 1
        result.containsKey("duplicate")
    }

    @Unroll
    def "should create valid RedisCacheConfiguration with TTL: #seconds seconds"() {
        given: "duration with specific seconds"
        def duration = Duration.ofSeconds(seconds)

        when: "creating cache config"
        def result = cacheProperties.createCacheConfig(duration)

        then: "should return valid configuration"
        result != null
        result instanceof RedisCacheConfiguration
        result.getTtl() == duration
        !result.usePrefix() // disableKeyPrefix() was called

        where:
        seconds << [0, 1, 60, 1800, 3600, 86400, -100]
    }

    def "should create cache config with correct serializers"() {
        given: "a duration"
        def duration = Duration.ofSeconds(3600)

        when: "creating cache config"
        def result = cacheProperties.createCacheConfig(duration)

        then: "should have correct serializers configured"
        result.getKeySerializationPair() != null
        result.getValueSerializationPair() != null
        result.getKeySerializationPair().getSerializer() instanceof StringRedisSerializer
        result.getValueSerializationPair().getSerializer() instanceof GenericJackson2JsonRedisSerializer
    }

    def "should throw exception when createCacheConfig receives null duration"() {
        when: "creating cache config with null duration"
        cacheProperties.createCacheConfig(null)

        then: "should throw NullPointerException"
        thrown(NullPointerException)
    }

    def "should calculate seconds until expiration for future time"() {
        given: "future time (2 hours from now)"
        def futureTime = LocalTime.now().plusHours(2)

        when: "calculating seconds until expiration"
        def result = cacheProperties.calculateSecondsUntilExpiration(futureTime)

        then: "should return positive seconds"
        result > 0
        result <= Duration.ofHours(2).getSeconds()
    }

    def "should calculate seconds until expiration for past time (next day)"() {
        given: "past time (2 hours ago)"
        def pastTime = LocalTime.now().minusHours(2)

        when: "calculating seconds until expiration"
        def result = cacheProperties.calculateSecondsUntilExpiration(pastTime)

        then: "should return positive seconds for next day"
        result > 0
        result > Duration.ofHours(20).getSeconds() // Should be around 22 hours
        result <= Duration.ofDays(1).getSeconds()
    }

    def "should handle midnight expiration time"() {
        when: "calculating seconds until midnight"
        def result = cacheProperties.calculateSecondsUntilExpiration(LocalTime.MIDNIGHT)

        then: "should return valid seconds"
        result >= 0
        result <= Duration.ofDays(1).getSeconds()
    }

    def "should handle noon expiration time"() {
        when: "calculating seconds until noon"
        def result = cacheProperties.calculateSecondsUntilExpiration(LocalTime.NOON)

        then: "should return valid seconds"
        result >= 0
        result <= Duration.ofDays(1).getSeconds()
    }

    def "should create StringRedisSerializer for key serialization"() {
        when: "getting key serializer pair"
        def result = cacheProperties.getKeySerializerPair()

        then: "should return StringRedisSerializer"
        result != null
        result instanceof RedisSerializationContext.SerializationPair
        result.getSerializer() instanceof StringRedisSerializer
    }

    def "should create GenericJackson2JsonRedisSerializer for value serialization"() {
        when: "getting value serializer pair"
        def result = cacheProperties.getValueSerializerPair()

        then: "should return GenericJackson2JsonRedisSerializer"
        result != null
        result instanceof RedisSerializationContext.SerializationPair
        result.getSerializer() instanceof GenericJackson2JsonRedisSerializer
    }

    def "should create properly configured ObjectMapper"() {
        when: "getting serializer mapper"
        def result = cacheProperties.getSerializerMapper()

        then: "should return configured ObjectMapper"
        result != null
        result instanceof ObjectMapper
        
        and: "should have JavaTimeModule registered"
        result.getRegisteredModuleIds().contains("jackson-datatype-jsr310")
    }

    def "should handle invalid time format in expireAtTime"() {
        given: "invalid time format"
        cacheProperties.expireAtTime = ["invalid": "25:00"]

        when: "getting initial cache configurations"
        cacheProperties.getInitialCacheConfigurations()

        then: "should throw DateTimeParseException"
        thrown(DateTimeParseException)
    }

    def "should handle null time value in expireAtTime"() {
        given: "null time value"
        cacheProperties.expireAtTime = ["null-time": null]

        when: "getting initial cache configurations"
        cacheProperties.getInitialCacheConfigurations()

        then: "should throw NullPointerException"
        thrown(NullPointerException)
    }

    def "should handle null TTL value in expireSeconds"() {
        given: "null TTL value"
        cacheProperties.expireSeconds = ["null-ttl": null]

        when: "getting initial cache configurations"
        cacheProperties.getInitialCacheConfigurations()

        then: "should throw NullPointerException"
        thrown(NullPointerException)
    }

    def "should handle empty string time in expireAtTime"() {
        given: "empty string time"
        cacheProperties.expireAtTime = ["empty": ""]

        when: "getting initial cache configurations"
        cacheProperties.getInitialCacheConfigurations()

        then: "should throw DateTimeParseException"
        thrown(DateTimeParseException)
    }

    def "should handle zero TTL in expireSeconds"() {
        given: "zero TTL"
        cacheProperties.expireSeconds = ["zero": 0L]

        when: "getting initial cache configurations"
        def result = cacheProperties.getInitialCacheConfigurations()

        then: "should create configuration with zero duration"
        result.containsKey("zero")
        result["zero"].getTtl() == Duration.ofSeconds(0)
    }

    def "should handle negative TTL in expireSeconds"() {
        given: "negative TTL"
        cacheProperties.expireSeconds = ["negative": -100L]

        when: "getting initial cache configurations"
        def result = cacheProperties.getInitialCacheConfigurations()

        then: "should create configuration with negative duration"
        result.containsKey("negative")
        result["negative"].getTtl() == Duration.ofSeconds(-100)
    }

    def "should handle large TTL values"() {
        given: "large TTL value"
        def largeTtl = Long.MAX_VALUE
        cacheProperties.expireSeconds = ["large": largeTtl]

        when: "getting initial cache configurations"
        def result = cacheProperties.getInitialCacheConfigurations()

        then: "should create configuration with large duration"
        result.containsKey("large")
        result["large"].getTtl() == Duration.ofSeconds(largeTtl)
    }

    def "should create new map instance for each call to getInitialCacheConfigurations"() {
        given: "cache properties with configuration"
        cacheProperties.expireSeconds = ["test": 1800L]

        when: "calling getInitialCacheConfigurations multiple times"
        def result1 = cacheProperties.getInitialCacheConfigurations()
        def result2 = cacheProperties.getInitialCacheConfigurations()

        then: "should return different map instances"
        result1 !== result2
        result1 == result2 // Same content
    }

    def "should create consistent serializer pairs"() {
        when: "creating multiple serializer pairs"
        def keyPair1 = cacheProperties.getKeySerializerPair()
        def keyPair2 = cacheProperties.getKeySerializerPair()
        def valuePair1 = cacheProperties.getValueSerializerPair()
        def valuePair2 = cacheProperties.getValueSerializerPair()

        then: "should have same serializer types"
        keyPair1.getSerializer().class == keyPair2.getSerializer().class
        valuePair1.getSerializer().class == valuePair2.getSerializer().class
    }

    def "should create new ObjectMapper instance for each call"() {
        when: "calling getSerializerMapper multiple times"
        def mapper1 = cacheProperties.getSerializerMapper()
        def mapper2 = cacheProperties.getSerializerMapper()

        then: "should return different instances with same configuration"
        mapper1 !== mapper2
        mapper1.class == mapper2.class
        mapper1.getRegisteredModuleIds() == mapper2.getRegisteredModuleIds()
    }

    def "should handle special characters in cache names"() {
        given: "cache names with special characters"
        cacheProperties.expireSeconds = [
            "cache-with-dashes": 1800L,
            "cache_with_underscores": 3600L,
            "cache.with.dots": 7200L,
            "cache:with:colons": 1200L,
            "": 900L // empty string
        ]

        when: "getting initial cache configurations"
        def result = cacheProperties.getInitialCacheConfigurations()

        then: "should handle all special characters"
        result.size() == 5
        result.containsKey("cache-with-dashes")
        result.containsKey("cache_with_underscores")
        result.containsKey("cache.with.dots")
        result.containsKey("cache:with:colons")
        result.containsKey("")
    }

    def "should verify ObjectMapper configuration details"() {
        when: "getting serializer mapper"
        def mapper = cacheProperties.getSerializerMapper()

        then: "should have specific configuration"
        mapper != null
        mapper.getRegisteredModuleIds().contains("jackson-datatype-jsr310")
        mapper.getDefaultTyping() != null
        mapper.getPolymorphicTypeValidator() != null
    }

    def "should handle time parsing edge cases"() {
        given: "edge case times"
        cacheProperties.expireAtTime = [
            "midnight": "00:00",
            "almost-midnight": "23:59",
            "noon": "12:00",
            "early-morning": "00:01"
        ]

        when: "getting initial cache configurations"
        def result = cacheProperties.getInitialCacheConfigurations()

        then: "should handle all edge case times"
        result.size() == 4
        result.values().every { it instanceof RedisCacheConfiguration }
        result.values().every { it.getTtl().getSeconds() >= 0 }
    }

    def "should verify RedisCacheConfiguration builder chain"() {
        given: "a duration"
        def duration = Duration.ofSeconds(1800)

        when: "creating cache config"
        def result = cacheProperties.createCacheConfig(duration)

        then: "should have all builder methods applied"
        !result.usePrefix() // disableKeyPrefix()
        result.getTtl() == duration // entryTtl()
        result.getKeySerializationPair() != null // serializeKeysWith()
        result.getValueSerializationPair() != null // serializeValuesWith()
    }

    def "should handle concurrent modifications to internal maps"() {
        given: "initial configuration"
        cacheProperties.expireSeconds = ["test": 1800L]

        when: "modifying map during processing"
        def originalMap = cacheProperties.expireSeconds
        def result = cacheProperties.getInitialCacheConfigurations()
        originalMap.put("new-cache", 3600L)

        and: "getting configurations again"
        def result2 = cacheProperties.getInitialCacheConfigurations()

        then: "should reflect the changes"
        result.size() == 1
        result2.size() == 2
        result2.containsKey("new-cache")
    }

    def "should verify method isolation and independence"() {
        when: "calling individual methods"
        def keyPair = cacheProperties.getKeySerializerPair()
        def valuePair = cacheProperties.getValueSerializerPair()
        def mapper = cacheProperties.getSerializerMapper()
        def config = cacheProperties.createCacheConfig(Duration.ofSeconds(1800))

        then: "each method should work independently"
        keyPair != null
        valuePair != null
        mapper != null
        config != null

        and: "should not affect each other"
        keyPair.getSerializer() instanceof StringRedisSerializer
        valuePair.getSerializer() instanceof GenericJackson2JsonRedisSerializer
        mapper instanceof ObjectMapper
        config instanceof RedisCacheConfiguration
    }

    def "should handle extreme time calculations"() {
        when: "calculating for extreme times"
        def secondBeforeMidnight = cacheProperties.calculateSecondsUntilExpiration(LocalTime.of(23, 59, 59))
        def secondAfterMidnight = cacheProperties.calculateSecondsUntilExpiration(LocalTime.of(0, 0, 1))

        then: "should handle extreme cases correctly"
        secondBeforeMidnight >= 0
        secondAfterMidnight >= 0
        secondBeforeMidnight < 60 // Should be less than 1 minute
        secondAfterMidnight > Duration.ofHours(23).getSeconds() // Should be almost 24 hours
    }

    def "should verify null safety of internal methods"() {
        when: "testing null safety"
        def keyPair = cacheProperties.getKeySerializerPair()
        def valuePair = cacheProperties.getValueSerializerPair()
        def mapper = cacheProperties.getSerializerMapper()

        then: "should never return null"
        keyPair != null
        valuePair != null
        mapper != null
        keyPair.getSerializer() != null
        valuePair.getSerializer() != null
    }

    private Object getPrivateField(Object target, String fieldName) {
        def field = target.class.getDeclaredField(fieldName)
        field.setAccessible(true)
        return field.get(target)
    }
}
