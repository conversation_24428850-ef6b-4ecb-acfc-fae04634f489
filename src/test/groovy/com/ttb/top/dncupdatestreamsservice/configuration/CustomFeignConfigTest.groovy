package com.ttb.top.dncupdatestreamsservice.configuration

import com.fasterxml.jackson.databind.ObjectMapper
import com.ttb.top.library.requestloghelper.model.RequestLogMessage
import com.ttb.top.library.requestloghelper.service.RequestLogPublisher
import com.ttb.top.library.requestloghelper.util.MaskingUtil
import feign.Request
import feign.RequestTemplate
import feign.Response
import feign.Target
import spock.lang.Specification
import spock.lang.Subject
import spock.lang.Unroll

import java.nio.charset.StandardCharsets

class CustomFeignConfigTest extends Specification {

    def requestLogPublisher = Mock(RequestLogPublisher)
    def maskingUtil = Mock(MaskingUtil)

    @Subject
    CustomFeignConfig customFeignConfig

    def setup() {
        customFeignConfig = new CustomFeignConfig()
        customFeignConfig.publisher = requestLogPublisher
        customFeignConfig.maskingUtil = maskingUtil
        
        // Set default values using reflection
        setPrivateField(customFeignConfig, "limitContent", 5000)
        setPrivateField(customFeignConfig, "excludedEndpoints", ["/health", "/actuator"] as String[])
        setPrivateField(customFeignConfig, "feignEnable", true)
        setPrivateField(customFeignConfig, "topic", "test-topic")
    }

    def "should create feignLoggerLevel bean with BASIC level"() {
        when: "creating feign logger level"
        def result = customFeignConfig.feignLoggerLevel()

        then: "should return BASIC level"
        result == feign.Logger.Level.BASIC
    }

    def "should verify class annotations"() {
        expect: "class should have correct annotations"
        CustomFeignConfig.class.isAnnotationPresent(org.springframework.context.annotation.Configuration.class)
        CustomFeignConfig.class.isAnnotationPresent(org.springframework.retry.annotation.EnableRetry.class)
    }

    def "should verify ResponseDTO inner class structure"() {
        when: "creating ResponseDTO"
        def responseDTO = new CustomFeignConfig.ResponseDTO(200, "http://test.com", [:], "test body")

        then: "should have correct properties"
        responseDTO.status == 200
        responseDTO.uri == "http://test.com"
        responseDTO.headers == [:]
        responseDTO.body == "test body"
    }

    def "should verify ResponseDTO builder pattern"() {
        when: "using builder pattern"
        def responseDTO = CustomFeignConfig.ResponseDTO.builder()
            .status(201)
            .uri("http://example.com")
            .headers(["Content-Type": ["application/json"]])
            .body("response body")
            .build()

        then: "should build correctly"
        responseDTO.status == 201
        responseDTO.uri == "http://example.com"
        responseDTO.headers["Content-Type"] == ["application/json"]
        responseDTO.body == "response body"
    }

    @Unroll
    def "should filter URLs correctly - URL: #url, shouldNotFilter: #expectedResult"() {
        when: "checking if URL should not be filtered"
        def result = customFeignConfig.isShouldNotFilter(url)

        then: "should return expected result"
        result == expectedResult

        where:
        url                           | expectedResult
        "http://test.com/health"      | true
        "http://test.com/actuator"    | true
        "http://test.com/api/users"   | false
        "http://test.com/health/check"| true
        null                          | true
        ""                            | false
    }

    def "should handle null URL in isShouldNotFilter"() {
        when: "checking null URL"
        def result = customFeignConfig.isShouldNotFilter(null)

        then: "should return true (should not filter)"
        result == true
    }

    def "should truncate string correctly"() {
        when: "truncating string"
        def result = customFeignConfig.truncateString(input, maxLength)

        then: "should return expected result"
        result == expected

        where:
        input           | maxLength | expected
        "Hello World"   | 5         | "Hello..."
        "Hello"         | 10        | "Hello"
        null            | 5         | ""
        ""              | 5         | ""
        "Test"          | 4         | "Test"
        "Testing"       | 4         | "Test..."
    }

    def "should handle getQueryParams correctly"() {
        when: "extracting query params"
        def result = customFeignConfig.getQueryParams(params)

        then: "should return expected result"
        result == expected

        where:
        params              | expected
        "?name=test&id=123" | "name=test&id=123"
        "name=test"         | null
        ""                  | null
        null                | null
        "?"                 | ""
    }

    def "should create URI correctly"() {
        when: "creating URI from URL"
        def result = customFeignConfig.getURI("http://example.com:8080/api/test")

        then: "should return valid URI"
        result != null
        result.host == "example.com"
        result.port == 8080
        result.path == "/api/test"
    }

    def "should remove host from URI correctly"() {
        when: "removing host from URI"
        def result = customFeignConfig.removeHostFromURI("http://example.com", "http://example.com/api/test")

        then: "should return URI without host"
        result == "/api/test"
    }

    def "should limit content correctly"() {
        given: "content longer than limit"
        setPrivateField(customFeignConfig, "limitContent", 10)
        def longContent = "This is a very long content that exceeds the limit"

        when: "limiting content"
        def result = customFeignConfig.limitContent(longContent)

        then: "should truncate to limit"
        result.length() == 10
        result == "This is a "
    }

    def "should handle null content in limitContent"() {
        when: "limiting null content"
        def result = customFeignConfig.limitContent(null)

        then: "should return null"
        result == null
    }

    def "should handle short content in limitContent"() {
        given: "content shorter than limit"
        setPrivateField(customFeignConfig, "limitContent", 100)
        def shortContent = "Short"

        when: "limiting content"
        def result = customFeignConfig.limitContent(shortContent)

        then: "should return original content"
        result == "Short"
    }

    def "should verify field annotations"() {
        when: "checking field annotations"
        def limitContentField = CustomFeignConfig.class.getDeclaredField("limitContent")
        def excludedEndpointsField = CustomFeignConfig.class.getDeclaredField("excludedEndpoints")
        def feignEnableField = CustomFeignConfig.class.getDeclaredField("feignEnable")
        def topicField = CustomFeignConfig.class.getDeclaredField("topic")

        then: "should have @Value annotations"
        limitContentField.isAnnotationPresent(org.springframework.beans.factory.annotation.Value.class)
        excludedEndpointsField.isAnnotationPresent(org.springframework.beans.factory.annotation.Value.class)
        feignEnableField.isAnnotationPresent(org.springframework.beans.factory.annotation.Value.class)
        topicField.isAnnotationPresent(org.springframework.beans.factory.annotation.Value.class)

        and: "should have correct default values"
        limitContentField.getAnnotation(org.springframework.beans.factory.annotation.Value.class).value() == '${app.api.logging.max-length}'
        excludedEndpointsField.getAnnotation(org.springframework.beans.factory.annotation.Value.class).value() == '${app.api.logging.feign.exclude-url-patterns:}'
        feignEnableField.getAnnotation(org.springframework.beans.factory.annotation.Value.class).value() == '${app.api.logging.feign.enable:true}'
        topicField.getAnnotation(org.springframework.beans.factory.annotation.Value.class).value() == '${request-log.topic:}'
    }

    def "should verify dependency injection annotations"() {
        when: "checking dependency injection"
        def publisherField = CustomFeignConfig.class.getDeclaredField("publisher")
        def maskingUtilField = CustomFeignConfig.class.getDeclaredField("maskingUtil")

        then: "should have @Autowired annotations"
        publisherField.isAnnotationPresent(org.springframework.beans.factory.annotation.Autowired.class)
        maskingUtilField.isAnnotationPresent(org.springframework.beans.factory.annotation.Autowired.class)
    }

    def "should verify static fields"() {
        when: "checking static fields"
        def logField = CustomFeignConfig.class.getDeclaredField("log")
        def mapperField = CustomFeignConfig.class.getDeclaredField("mapper")

        then: "should be static and final"
        java.lang.reflect.Modifier.isStatic(logField.modifiers)
        java.lang.reflect.Modifier.isFinal(logField.modifiers)
        java.lang.reflect.Modifier.isStatic(mapperField.modifiers)
        java.lang.reflect.Modifier.isFinal(mapperField.modifiers)

        and: "should have correct types"
        logField.type == org.slf4j.Logger.class
        mapperField.type == ObjectMapper.class
    }

    def "should extend Slf4jLogger"() {
        expect: "CustomFeignConfig should extend Slf4jLogger"
        feign.slf4j.Slf4jLogger.class.isAssignableFrom(CustomFeignConfig.class)
    }

    def "should handle empty excluded endpoints"() {
        given: "empty excluded endpoints"
        setPrivateField(customFeignConfig, "excludedEndpoints", [] as String[])

        when: "checking if URL should not be filtered"
        def result = customFeignConfig.isShouldNotFilter("http://test.com/api")

        then: "should return false (should filter)"
        result == false
    }

    def "should handle single excluded endpoint"() {
        given: "single excluded endpoint"
        setPrivateField(customFeignConfig, "excludedEndpoints", ["/health"] as String[])

        when: "checking URLs"
        def healthResult = customFeignConfig.isShouldNotFilter("http://test.com/health")
        def apiResult = customFeignConfig.isShouldNotFilter("http://test.com/api")

        then: "should filter correctly"
        healthResult == true
        apiResult == false
    }

    def "should verify bean method annotation"() {
        when: "checking feignLoggerLevel method"
        def method = CustomFeignConfig.class.getMethod("feignLoggerLevel")

        then: "should have @Bean annotation"
        method.isAnnotationPresent(org.springframework.context.annotation.Bean.class)
    }

    private void setPrivateField(Object target, String fieldName, Object value) {
        def field = target.class.getDeclaredField(fieldName)
        field.setAccessible(true)
        field.set(target, value)
    }
}
