package com.ttb.top.dncupdatestreamsservice.configuration

import com.ttb.top.library.requestloghelper.model.RequestLogMessage
import com.ttb.top.library.requestloghelper.service.RequestLogPublisher
import com.ttb.top.library.requestloghelper.util.MaskingUtil
import feign.Request
import feign.RequestTemplate
import feign.Response
import feign.Target
import spock.lang.Specification
import spock.lang.Subject

import java.nio.charset.StandardCharsets

class CustomFeignConfigResponseTest extends Specification {

    def requestLogPublisher = Mock(RequestLogPublisher)
    def maskingUtil = Mock(MaskingUtil)

    @Subject
    CustomFeignConfig customFeignConfig

    def mockRequest = Mock(Request)
    def mockResponse = Mock(Response)
    def mockRequestTemplate = Mock(RequestTemplate)
    def mockTarget = Mock(Target)

    def setup() {
        customFeignConfig = new CustomFeignConfig()
        customFeignConfig.publisher = requestLogPublisher
        customFeignConfig.maskingUtil = maskingUtil
        
        // Set default values
        setPrivateField(customFeignConfig, "limitContent", 5000)
        setPrivateField(customFeignConfig, "excludedEndpoints", ["/health"] as String[])
        setPrivateField(customFeignConfig, "feignEnable", true)
        setPrivateField(customFeignConfig, "topic", "test-topic")

        // Setup common mocks
        mockRequest.url() >> "http://example.com/api/test"
        mockRequest.headers() >> ["Content-Type": ["application/json"]]
        mockRequest.httpMethod() >> Request.HttpMethod.POST
        mockRequest.requestTemplate() >> mockRequestTemplate
        mockRequestTemplate.feignTarget() >> mockTarget
        mockTarget.name() >> "test-service"
        mockTarget.url() >> "http://example.com"
        mockRequestTemplate.path() >> "/api/test"
        mockRequestTemplate.queryLine() >> "?param=value"
        
        mockResponse.status() >> 200
        mockResponse.headers() >> ["Content-Type": ["application/json"]]
        mockResponse.request() >> mockRequest
    }

    def "should handle logAndRebufferResponse successfully"() {
        given: "valid response with body"
        def responseBody = "test response body"
        def bodyStream = new ByteArrayInputStream(responseBody.bytes)
        def responseBodyMock = Mock(Response.Body)
        responseBodyMock.asInputStream() >> bodyStream
        
        mockResponse.body() >> responseBodyMock
        mockResponse.toBuilder() >> Mock(Response.Builder) {
            body(_) >> Mock(Response.Builder) {
                build() >> mockResponse
            }
        }

        when: "processing response"
        def result = customFeignConfig.logAndRebufferResponse("test-config", feign.Logger.Level.BASIC, mockResponse, 1000L)

        then: "should return response"
        result == mockResponse
    }

    def "should handle logAndRebufferResponse with excluded URL"() {
        given: "response with excluded URL"
        mockRequest.url() >> "http://example.com/health"
        def responseBody = "health check response"
        def bodyStream = new ByteArrayInputStream(responseBody.bytes)
        def responseBodyMock = Mock(Response.Body)
        responseBodyMock.asInputStream() >> bodyStream
        
        mockResponse.body() >> responseBodyMock
        mockResponse.toBuilder() >> Mock(Response.Builder) {
            body(_) >> Mock(Response.Builder) {
                build() >> mockResponse
            }
        }

        when: "processing response for excluded URL"
        def result = customFeignConfig.logAndRebufferResponse("test-config", feign.Logger.Level.BASIC, mockResponse, 1000L)

        then: "should not publish log message"
        0 * requestLogPublisher.publishMessage(_, _)
        result == mockResponse
    }

    def "should handle logAndRebufferResponse with feign logging disabled"() {
        given: "feign logging disabled"
        setPrivateField(customFeignConfig, "feignEnable", false)
        def responseBody = "test response"
        def bodyStream = new ByteArrayInputStream(responseBody.bytes)
        def responseBodyMock = Mock(Response.Body)
        responseBodyMock.asInputStream() >> bodyStream
        
        mockResponse.body() >> responseBodyMock
        mockResponse.toBuilder() >> Mock(Response.Builder) {
            body(_) >> Mock(Response.Builder) {
                build() >> mockResponse
            }
        }

        when: "processing response with logging disabled"
        def result = customFeignConfig.logAndRebufferResponse("test-config", feign.Logger.Level.BASIC, mockResponse, 1000L)

        then: "should not publish log message"
        0 * requestLogPublisher.publishMessage(_, _)
        result == mockResponse
    }

    def "should handle exception in logAndRebufferResponse"() {
        given: "response that throws exception"
        mockResponse.body() >> { throw new RuntimeException("Test exception") }

        when: "processing response that throws exception"
        def result = customFeignConfig.logAndRebufferResponse("test-config", feign.Logger.Level.BASIC, mockResponse, 1000L)

        then: "should handle exception gracefully"
        result == mockResponse
        0 * requestLogPublisher.publishMessage(_, _)
    }

    def "should handle setRequest method correctly"() {
        given: "request with body"
        def requestBody = "test request body"
        mockRequest.body() >> requestBody.bytes
        maskingUtil.maskJsonString(_) >> "masked body"
        maskingUtil.maskMap(_) >> ["masked": "headers"]

        when: "setting request in log message"
        def logMessage = new RequestLogMessage()
        def result = customFeignConfig.setRequest(logMessage, mockRequest)

        then: "should populate request fields"
        result != null
        result.reqMethod == "POST"
        result.reqBody == "masked body"
        result.service == "test-service"
        1 * maskingUtil.maskJsonString(_)
        1 * maskingUtil.maskMap(_)
    }

    def "should handle setRequest with null body"() {
        given: "request without body"
        mockRequest.body() >> null

        when: "setting request without body"
        def logMessage = new RequestLogMessage()
        def result = customFeignConfig.setRequest(logMessage, mockRequest)

        then: "should handle null body gracefully"
        result != null
        result.reqMethod == "POST"
        result.reqBody == null
        0 * maskingUtil.maskJsonString(_)
    }

    def "should handle setResponse method correctly"() {
        given: "response with body data"
        def responseBody = "test response body"
        maskingUtil.maskJsonString(_) >> "masked response"

        when: "setting response in log message"
        def logMessage = new RequestLogMessage()
        def result = customFeignConfig.setResponse(logMessage, mockResponse, responseBody.bytes, 1500L)

        then: "should populate response fields"
        result != null
        result.respHttpStatus == 200
        result.executeTime == 1500L
        result.respBody == "masked response"
        1 * maskingUtil.maskJsonString(_)
    }

    def "should handle exception in setRequest"() {
        given: "request that causes exception in processing"
        mockRequest.body() >> "test".bytes
        maskingUtil.maskJsonString(_) >> { throw new RuntimeException("Masking error") }

        when: "setting request that throws exception"
        def logMessage = new RequestLogMessage()
        def result = customFeignConfig.setRequest(logMessage, mockRequest)

        then: "should handle exception gracefully"
        result != null
        // Should not throw exception, just log error
        noExceptionThrown()
    }

    def "should handle exception in setResponse"() {
        given: "response processing that throws exception"
        maskingUtil.maskJsonString(_) >> { throw new RuntimeException("Masking error") }

        when: "setting response that throws exception"
        def logMessage = new RequestLogMessage()
        def result = customFeignConfig.setResponse(logMessage, mockResponse, "test".bytes, 1000L)

        then: "should handle exception gracefully"
        result != null
        noExceptionThrown()
    }

    def "should handle logHandler method correctly"() {
        given: "valid response and body data"
        def responseBody = "test response"
        mockRequest.body() >> "request body".bytes
        maskingUtil.maskJsonString(_) >> "masked content"
        maskingUtil.maskMap(_) >> ["masked": "headers"]

        when: "handling log"
        customFeignConfig.logHandler(mockResponse, responseBody.bytes, 2000L)

        then: "should publish log message"
        1 * requestLogPublisher.publishMessage("test-topic", _)
        1 * maskingUtil.maskJsonString(_)
        1 * maskingUtil.maskMap(_)
    }

    def "should handle exception in logHandler"() {
        given: "log handler that throws exception"
        requestLogPublisher.publishMessage(_, _) >> { throw new RuntimeException("Publisher error") }

        when: "handling log that throws exception"
        customFeignConfig.logHandler(mockResponse, "test".bytes, 1000L)

        then: "should handle exception gracefully"
        noExceptionThrown()
    }

    def "should handle large response body"() {
        given: "large response body"
        setPrivateField(customFeignConfig, "limitContent", 10)
        def largeBody = "This is a very large response body that exceeds the limit"
        def bodyStream = new ByteArrayInputStream(largeBody.bytes)
        def responseBodyMock = Mock(Response.Body)
        responseBodyMock.asInputStream() >> bodyStream
        
        mockResponse.body() >> responseBodyMock
        mockResponse.toBuilder() >> Mock(Response.Builder) {
            body(_) >> Mock(Response.Builder) {
                build() >> mockResponse
            }
        }

        when: "processing large response"
        def result = customFeignConfig.logAndRebufferResponse("test-config", feign.Logger.Level.BASIC, mockResponse, 1000L)

        then: "should handle large body correctly"
        result == mockResponse
    }

    def "should handle URL decoding in logInbound"() {
        given: "URL with encoded characters"
        mockRequest.url() >> "http://example.com/api/test%20space"
        def responseBody = "test response"
        def bodyStream = new ByteArrayInputStream(responseBody.bytes)
        def responseBodyMock = Mock(Response.Body)
        responseBodyMock.asInputStream() >> bodyStream
        
        mockResponse.body() >> responseBodyMock
        mockResponse.toBuilder() >> Mock(Response.Builder) {
            body(_) >> Mock(Response.Builder) {
                build() >> mockResponse
            }
        }

        when: "processing response with encoded URL"
        def result = customFeignConfig.logAndRebufferResponse("test-config", feign.Logger.Level.BASIC, mockResponse, 1000L)

        then: "should decode URL correctly"
        result == mockResponse
        noExceptionThrown()
    }

    private void setPrivateField(Object target, String fieldName, Object value) {
        def field = target.class.getDeclaredField(fieldName)
        field.setAccessible(true)
        field.set(target, value)
    }
}
