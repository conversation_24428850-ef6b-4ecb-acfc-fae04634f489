package com.ttb.top.dncupdatestreamsservice.configuration

import org.springframework.cache.CacheManager
import org.springframework.cache.support.NoOpCacheManager
import org.springframework.data.redis.cache.RedisCacheConfiguration
import org.springframework.data.redis.cache.RedisCacheManager
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory
import spock.lang.Specification
import spock.lang.Subject

import java.time.Duration

class RedisCacheConfigTest extends Specification {

    def cacheProperties = Mock(CacheProperties)
    def lettuceConnectionFactory = Mock(LettuceConnectionFactory)

    @Subject
    RedisCacheConfig redisCacheConfig

    def setup() {
        redisCacheConfig = new RedisCacheConfig(cacheProperties)
        // Simulate @Value injection for defaultCacheTtl
        setPrivateField(redisCacheConfig, "defaultCacheTtl", "86400")
    }

    def "should create NoOpCacheManager when redis.enabled is false or missing"() {
        when: "creating NoOp cache manager"
        CacheManager cacheManager = redisCacheConfig.noOpCacheManager()

        then: "should return NoOpCacheManager instance"
        cacheManager instanceof NoOpCacheManager
        cacheManager.getCacheNames().isEmpty()
    }

    def "should create RedisCacheManager when redis.enabled is true"() {
        given: "cache properties return configurations"
        def mockCacheConfig = Mock(RedisCacheConfiguration)
        def initialConfigurations = ["test-cache": mockCacheConfig]
        
        cacheProperties.getInitialCacheConfigurations() >> initialConfigurations
        cacheProperties.createCacheConfig(_) >> mockCacheConfig

        when: "creating Redis cache manager"
        RedisCacheManager cacheManager = redisCacheConfig.customRedisCacheManager(lettuceConnectionFactory)

        then: "should return RedisCacheManager instance"
        cacheManager instanceof RedisCacheManager
        
        and: "should call cache properties methods"
        1 * cacheProperties.getInitialCacheConfigurations()
        1 * cacheProperties.createCacheConfig({ Duration duration ->
            duration.seconds == 86400
        })
    }

    def "should create default cache configuration with correct TTL"() {
        given: "cache properties mock returns configuration"
        def mockCacheConfig = Mock(RedisCacheConfiguration)
        cacheProperties.createCacheConfig(_) >> mockCacheConfig

        when: "creating default cache config"
        def result = redisCacheConfig.createDefaultCacheConfig()

        then: "should call createCacheConfig with default TTL duration"
        1 * cacheProperties.createCacheConfig({ Duration duration ->
            duration.seconds == 86400
        })
        result == mockCacheConfig
    }

    def "should handle custom TTL values"() {
        given: "custom TTL configuration"
        setPrivateField(redisCacheConfig, "defaultCacheTtl", "3600")
        def mockCacheConfig = Mock(RedisCacheConfiguration)
        cacheProperties.createCacheConfig(_) >> mockCacheConfig

        when: "creating default cache config with custom TTL"
        def result = redisCacheConfig.createDefaultCacheConfig()

        then: "should use custom TTL value"
        1 * cacheProperties.createCacheConfig({ Duration duration ->
            duration.seconds == 3600
        })
        result == mockCacheConfig
    }

    def "should handle zero TTL value"() {
        given: "zero TTL configuration"
        setPrivateField(redisCacheConfig, "defaultCacheTtl", "0")
        def mockCacheConfig = Mock(RedisCacheConfiguration)
        cacheProperties.createCacheConfig(_) >> mockCacheConfig

        when: "creating default cache config with zero TTL"
        def result = redisCacheConfig.createDefaultCacheConfig()

        then: "should use zero TTL value"
        1 * cacheProperties.createCacheConfig({ Duration duration ->
            duration.seconds == 0
        })
        result == mockCacheConfig
    }

    def "should handle large TTL values"() {
        given: "large TTL configuration"
        setPrivateField(redisCacheConfig, "defaultCacheTtl", "604800") // 7 days
        def mockCacheConfig = Mock(RedisCacheConfiguration)
        cacheProperties.createCacheConfig(_) >> mockCacheConfig

        when: "creating default cache config with large TTL"
        def result = redisCacheConfig.createDefaultCacheConfig()

        then: "should use large TTL value"
        1 * cacheProperties.createCacheConfig({ Duration duration ->
            duration.seconds == 604800
        })
        result == mockCacheConfig
    }

    def "should throw NumberFormatException for invalid TTL format"() {
        given: "invalid TTL configuration"
        setPrivateField(redisCacheConfig, "defaultCacheTtl", "invalid-number")

        when: "creating default cache config with invalid TTL"
        redisCacheConfig.createDefaultCacheConfig()

        then: "should throw NumberFormatException"
        thrown(NumberFormatException)
    }

    def "should throw NumberFormatException for empty TTL"() {
        given: "empty TTL configuration"
        setPrivateField(redisCacheConfig, "defaultCacheTtl", "")

        when: "creating default cache config with empty TTL"
        redisCacheConfig.createDefaultCacheConfig()

        then: "should throw NumberFormatException"
        thrown(NumberFormatException)
    }

    def "should throw NumberFormatException for null TTL"() {
        given: "null TTL configuration"
        setPrivateField(redisCacheConfig, "defaultCacheTtl", null)

        when: "creating default cache config with null TTL"
        redisCacheConfig.createDefaultCacheConfig()

        then: "should throw NumberFormatException"
        thrown(NumberFormatException)
    }

    def "should handle negative TTL values"() {
        given: "negative TTL configuration"
        setPrivateField(redisCacheConfig, "defaultCacheTtl", "-100")
        def mockCacheConfig = Mock(RedisCacheConfiguration)
        cacheProperties.createCacheConfig(_) >> mockCacheConfig

        when: "creating default cache config with negative TTL"
        def result = redisCacheConfig.createDefaultCacheConfig()

        then: "should use negative TTL value"
        1 * cacheProperties.createCacheConfig({ Duration duration ->
            duration.seconds == -100
        })
        result == mockCacheConfig
    }

    def "should create RedisCacheManager with empty initial configurations"() {
        given: "cache properties return empty configurations"
        cacheProperties.getInitialCacheConfigurations() >> [:]
        def mockCacheConfig = Mock(RedisCacheConfiguration)
        cacheProperties.createCacheConfig(_) >> mockCacheConfig

        when: "creating Redis cache manager with empty configurations"
        RedisCacheManager cacheManager = redisCacheConfig.customRedisCacheManager(lettuceConnectionFactory)

        then: "should create manager successfully"
        cacheManager instanceof RedisCacheManager
        1 * cacheProperties.getInitialCacheConfigurations()
        1 * cacheProperties.createCacheConfig(_)
    }

    def "should create RedisCacheManager with multiple initial configurations"() {
        given: "cache properties return multiple configurations"
        def mockCacheConfig1 = Mock(RedisCacheConfiguration)
        def mockCacheConfig2 = Mock(RedisCacheConfiguration)
        def mockDefaultConfig = Mock(RedisCacheConfiguration)
        
        def initialConfigurations = [
            "cache1": mockCacheConfig1,
            "cache2": mockCacheConfig2
        ]
        
        cacheProperties.getInitialCacheConfigurations() >> initialConfigurations
        cacheProperties.createCacheConfig(_) >> mockDefaultConfig

        when: "creating Redis cache manager with multiple configurations"
        RedisCacheManager cacheManager = redisCacheConfig.customRedisCacheManager(lettuceConnectionFactory)

        then: "should create manager with all configurations"
        cacheManager instanceof RedisCacheManager
        1 * cacheProperties.getInitialCacheConfigurations()
        1 * cacheProperties.createCacheConfig(_)
    }

    def "should handle null LettuceConnectionFactory"() {
        given: "cache properties setup"
        cacheProperties.getInitialCacheConfigurations() >> [:]
        def mockCacheConfig = Mock(RedisCacheConfiguration)
        cacheProperties.createCacheConfig(_) >> mockCacheConfig

        when: "creating Redis cache manager with null connection factory"
        redisCacheConfig.customRedisCacheManager(null)

        then: "should throw exception during RedisCacheManager creation"
        thrown(Exception)
    }

    def "should verify class annotations"() {
        expect: "class should have correct annotations"
        RedisCacheConfig.class.isAnnotationPresent(org.springframework.context.annotation.Configuration.class)
        
        def configAnnotation = RedisCacheConfig.class.getAnnotation(org.springframework.context.annotation.Configuration.class)
        configAnnotation.value() == "customerRedisCacheConfig"
    }

    def "should verify bean method annotations for noOpCacheManager"() {
        when: "checking noOpCacheManager method annotations"
        def method = RedisCacheConfig.class.getMethod("noOpCacheManager")
        
        then: "should have correct annotations"
        method.isAnnotationPresent(org.springframework.context.annotation.Bean.class)
        method.isAnnotationPresent(org.springframework.boot.autoconfigure.condition.ConditionalOnProperty.class)
        
        def beanAnnotation = method.getAnnotation(org.springframework.context.annotation.Bean.class)
        beanAnnotation.value() == ["customRedisCacheManager"] as String[]
        
        def conditionalAnnotation = method.getAnnotation(org.springframework.boot.autoconfigure.condition.ConditionalOnProperty.class)
        conditionalAnnotation.name() == "redis.enabled"
        conditionalAnnotation.havingValue() == "false"
        conditionalAnnotation.matchIfMissing() == true
    }

    def "should verify bean method annotations for customRedisCacheManager"() {
        when: "checking customRedisCacheManager method annotations"
        def method = RedisCacheConfig.class.getMethod("customRedisCacheManager", LettuceConnectionFactory.class)
        
        then: "should have correct annotations"
        method.isAnnotationPresent(org.springframework.context.annotation.Bean.class)
        method.isAnnotationPresent(org.springframework.boot.autoconfigure.condition.ConditionalOnProperty.class)
        
        def beanAnnotation = method.getAnnotation(org.springframework.context.annotation.Bean.class)
        beanAnnotation.value() == ["customRedisCacheManager"] as String[]
        
        def conditionalAnnotation = method.getAnnotation(org.springframework.boot.autoconfigure.condition.ConditionalOnProperty.class)
        conditionalAnnotation.name() == "redis.enabled"
        conditionalAnnotation.havingValue() == "true"
        conditionalAnnotation.matchIfMissing() == false
    }

    def "should verify field annotations"() {
        when: "checking defaultCacheTtl field annotations"
        def field = RedisCacheConfig.class.getDeclaredField("defaultCacheTtl")
        
        then: "should have @Value annotation with correct default"
        field.isAnnotationPresent(org.springframework.beans.factory.annotation.Value.class)
        
        def valueAnnotation = field.getAnnotation(org.springframework.beans.factory.annotation.Value.class)
        valueAnnotation.value() == '${cache.default.ttl:86400}'
    }

    private void setPrivateField(Object target, String fieldName, Object value) {
        def field = target.class.getDeclaredField(fieldName)
        field.setAccessible(true)
        field.set(target, value)
    }
}
