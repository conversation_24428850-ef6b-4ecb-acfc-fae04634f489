package com.ttb.top.dncupdatestreamsservice.configuration

import org.springframework.beans.factory.annotation.Value
import org.springframework.cache.CacheManager
import org.springframework.cache.support.NoOpCacheManager
import org.springframework.context.annotation.Configuration
import org.springframework.data.redis.cache.RedisCacheConfiguration
import spock.lang.Specification
import spock.lang.Subject

class RedisCacheConfigTest extends Specification {

    def cacheProperties = Mock(CacheProperties)

    @Subject
    RedisCacheConfig redisCacheConfig

    def setup() {
        redisCacheConfig = new RedisCacheConfig(cacheProperties)
        // Simulate @Value injection for defaultCacheTtl
        setPrivateField(redisCacheConfig, "defaultCacheTtl", "86400")
    }

    def "should create NoOpCacheManager when redis.enabled is false or missing"() {
        when: "creating NoOp cache manager"
        CacheManager cacheManager = redisCacheConfig.noOpCacheManager()

        then: "should return NoOpCacheManager instance"
        cacheManager instanceof NoOpCacheManager
        cacheManager.getCacheNames().isEmpty()
    }

    def "should throw NumberFormatException for invalid TTL format"() {
        given: "invalid TTL configuration"
        setPrivateField(redisCacheConfig, "defaultCacheTtl", "invalid-number")

        when: "creating default cache config with invalid TTL"
        redisCacheConfig.createDefaultCacheConfig()

        then: "should throw NumberFormatException"
        thrown(NumberFormatException)
    }

    def "should throw NumberFormatException for empty TTL"() {
        given: "empty TTL configuration"
        setPrivateField(redisCacheConfig, "defaultCacheTtl", "")

        when: "creating default cache config with empty TTL"
        redisCacheConfig.createDefaultCacheConfig()

        then: "should throw NumberFormatException"
        thrown(NumberFormatException)
    }

    def "should throw NumberFormatException for null TTL"() {
        given: "null TTL configuration"
        setPrivateField(redisCacheConfig, "defaultCacheTtl", null)

        when: "creating default cache config with null TTL"
        redisCacheConfig.createDefaultCacheConfig()

        then: "should throw NumberFormatException"
        thrown(NumberFormatException)
    }

    def "should handle null LettuceConnectionFactory"() {
        given: "cache properties setup"
        cacheProperties.getInitialCacheConfigurations() >> [:]
        def mockCacheConfig = Mock(RedisCacheConfiguration)
        cacheProperties.createCacheConfig(_) >> mockCacheConfig

        when: "creating Redis cache manager with null connection factory"
        redisCacheConfig.customRedisCacheManager(null)

        then: "should throw exception during RedisCacheManager creation"
        thrown(Exception)
    }

    def "should verify class annotations"() {
        expect: "class should have correct annotations"
        RedisCacheConfig.class.isAnnotationPresent(Configuration.class)

        def configAnnotation = RedisCacheConfig.class.getAnnotation(Configuration.class)
        configAnnotation.value() == "customerRedisCacheConfig"
    }

    def "should verify field annotations"() {
        when: "checking defaultCacheTtl field annotations"
        def field = RedisCacheConfig.class.getDeclaredField("defaultCacheTtl")

        then: "should have @Value annotation with correct default"
        field.isAnnotationPresent(Value.class)

        def valueAnnotation = field.getAnnotation(Value.class)
        valueAnnotation.value() == '${cache.default.ttl:86400}'
    }

    private static void setPrivateField(Object target, String fieldName, Object value) {
        def field = target.class.getDeclaredField(fieldName)
        field.setAccessible(true)
        field.set(target, value)
    }
}
