package com.ttb.top.dncupdatestreamsservice.configuration

import org.springframework.data.redis.cache.RedisCacheConfiguration
import org.springframework.data.redis.cache.RedisCacheManager
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory
import spock.lang.Specification
import spock.lang.Subject

import java.time.Duration

class RedisCacheConfigNullConfigTest extends Specification {

    def cacheProperties = Mock(CacheProperties)
    def lettuceConnectionFactory = Mock(LettuceConnectionFactory)

    @Subject
    RedisCacheConfig redisCacheConfig

    def setup() {
        redisCacheConfig = new RedisCacheConfig(cacheProperties)
        setPrivateField(redisCacheConfig, "defaultCacheTtl", "86400")
    }

    def "should not return null from createDefaultCacheConfig"() {
        given: "cache properties returns a valid configuration"
        def validCacheConfig = RedisCacheConfiguration.defaultCacheConfig()
        cacheProperties.createCacheConfig(_) >> validCacheConfig

        when: "creating default cache config"
        def result = redisCacheConfig.createDefaultCacheConfig()

        then: "result should not be null"
        result != null
        result instanceof RedisCacheConfiguration
    }

    def "should throw exception when cacheProperties.createCacheConfig returns null"() {
        given: "cache properties returns null"
        cacheProperties.createCacheConfig(_) >> null

        when: "creating default cache config"
        def result = redisCacheConfig.createDefaultCacheConfig()

        then: "result should be null (which will cause RedisCacheManager to fail)"
        result == null
    }

    def "should fail to create RedisCacheManager when default config is null"() {
        given: "cache properties returns null configuration"
        cacheProperties.getInitialCacheConfigurations() >> [:]
        cacheProperties.createCacheConfig(_) >> null

        when: "creating Redis cache manager with null default config"
        redisCacheConfig.customRedisCacheManager(lettuceConnectionFactory)

        then: "should throw IllegalArgumentException with 'must not be null' message"
        def exception = thrown(IllegalArgumentException)
        exception.message.contains("must not be null") || exception.message.contains("DefaultCacheConfiguration")
    }

    def "should successfully create RedisCacheManager when default config is valid"() {
        given: "cache properties returns valid configuration"
        def validCacheConfig = RedisCacheConfiguration.defaultCacheConfig()
        cacheProperties.getInitialCacheConfigurations() >> [:]
        cacheProperties.createCacheConfig(_) >> validCacheConfig

        when: "creating Redis cache manager with valid default config"
        def result = redisCacheConfig.customRedisCacheManager(lettuceConnectionFactory)

        then: "should create RedisCacheManager successfully"
        result != null
        result instanceof org.springframework.data.redis.cache.RedisCacheManager
    }

    def "should handle cacheProperties throwing exception"() {
        given: "cache properties throws exception"
        cacheProperties.createCacheConfig(_) >> { throw new RuntimeException("Cache config error") }

        when: "creating default cache config"
        redisCacheConfig.createDefaultCacheConfig()

        then: "should propagate the exception"
        thrown(RuntimeException)
    }

    def "should verify TTL is passed correctly to cacheProperties"() {
        given: "cache properties mock"
        def validCacheConfig = RedisCacheConfiguration.defaultCacheConfig()
        cacheProperties.createCacheConfig(_) >> validCacheConfig

        when: "creating default cache config"
        redisCacheConfig.createDefaultCacheConfig()

        then: "should call createCacheConfig with correct duration"
        1 * cacheProperties.createCacheConfig({ Duration duration ->
            duration.seconds == 86400
        })
    }

    def "should test with different TTL values to ensure proper duration creation"() {
        given: "different TTL values"
        setPrivateField(redisCacheConfig, "defaultCacheTtl", ttlValue)
        def validCacheConfig = RedisCacheConfiguration.defaultCacheConfig()
        cacheProperties.createCacheConfig(_) >> validCacheConfig

        when: "creating default cache config"
        redisCacheConfig.createDefaultCacheConfig()

        then: "should call createCacheConfig with correct duration"
        1 * cacheProperties.createCacheConfig({ Duration duration ->
            duration.seconds == expectedSeconds
        })

        where:
        ttlValue | expectedSeconds
        "3600"   | 3600
        "7200"   | 7200
        "0"      | 0
        "1"      | 1
    }

    def "should verify cacheProperties is not null"() {
        expect: "cacheProperties should be injected and not null"
        def cachePropertiesField = RedisCacheConfig.class.getDeclaredField("cacheProperties")
        cachePropertiesField.setAccessible(true)
        def injectedCacheProperties = cachePropertiesField.get(redisCacheConfig)
        injectedCacheProperties != null
    }

    def "should handle empty initial configurations"() {
        given: "empty initial configurations but valid default config"
        def validDefaultConfig = RedisCacheConfiguration.defaultCacheConfig()

        cacheProperties.createCacheConfig(_) >> validDefaultConfig
        cacheProperties.getInitialCacheConfigurations() >> [:]

        when: "creating Redis cache manager with empty initial configs"
        def result = redisCacheConfig.customRedisCacheManager(lettuceConnectionFactory)

        then: "should create manager successfully"
        result != null
    }

    def "should verify the exact error message for null default configuration"() {
        given: "cache properties returns null for default config"
        cacheProperties.getInitialCacheConfigurations() >> [:]
        cacheProperties.createCacheConfig(_) >> null

        when: "creating Redis cache manager"
        redisCacheConfig.customRedisCacheManager(lettuceConnectionFactory)

        then: "should throw IllegalArgumentException with specific message"
        def exception = thrown(IllegalArgumentException)
        exception.message.toLowerCase().contains("must not be null") ||
        exception.message.contains("DefaultCacheConfiguration")
    }

    def "should identify root cause when CacheProperties.createCacheConfig returns null"() {
        given: "cache properties that returns null"
        cacheProperties.createCacheConfig(_) >> null

        when: "checking what createDefaultCacheConfig returns"
        def result = redisCacheConfig.createDefaultCacheConfig()

        then: "result is null, which is the root cause of 'DefaultCacheConfiguration must not be null'"
        result == null

        and: "this null result will cause RedisCacheManager.builder().cacheDefaults(null) to fail"
        // This test documents the root cause of the issue
        true
    }

    def "should verify CacheProperties dependency injection issue"() {
        given: "RedisCacheConfig with null cacheProperties"
        def configWithNullProps = new RedisCacheConfig(null)
        setPrivateField(configWithNullProps, "defaultCacheTtl", "86400")

        when: "trying to create default cache config with null cacheProperties"
        configWithNullProps.createDefaultCacheConfig()

        then: "should throw NullPointerException"
        thrown(NullPointerException)
    }

    def "should provide solution test - ensure CacheProperties.createCacheConfig never returns null"() {
        given: "properly configured CacheProperties that never returns null"
        def validConfig = RedisCacheConfiguration.defaultCacheConfig()
        cacheProperties.createCacheConfig(_) >> validConfig
        cacheProperties.getInitialCacheConfigurations() >> [:]

        when: "creating Redis cache manager with guaranteed non-null config"
        def result = redisCacheConfig.customRedisCacheManager(lettuceConnectionFactory)

        then: "should succeed without 'DefaultCacheConfiguration must not be null' error"
        result != null
        result instanceof RedisCacheManager
        noExceptionThrown()
    }

    def "should test CacheProperties behavior with invalid Duration"() {
        given: "CacheProperties that might fail with invalid duration"
        cacheProperties.createCacheConfig(_) >> { Duration duration ->
            if (duration == null || duration.isNegative()) {
                return null // This could be the issue
            }
            return RedisCacheConfiguration.defaultCacheConfig()
        }

        when: "creating config with potentially problematic duration"
        setPrivateField(redisCacheConfig, "defaultCacheTtl", "-1")
        def result = redisCacheConfig.createDefaultCacheConfig()

        then: "might return null if CacheProperties doesn't handle negative duration"
        result == null
    }

    private void setPrivateField(Object target, String fieldName, Object value) {
        def field = target.class.getDeclaredField(fieldName)
        field.setAccessible(true)
        field.set(target, value)
    }
}
