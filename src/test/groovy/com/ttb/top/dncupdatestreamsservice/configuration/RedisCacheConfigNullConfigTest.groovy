package com.ttb.top.dncupdatestreamsservice.configuration

import org.springframework.data.redis.cache.RedisCacheConfiguration
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory
import spock.lang.Specification
import spock.lang.Subject

import java.time.Duration

class RedisCacheConfigNullConfigTest extends Specification {

    def cacheProperties = Mock(CacheProperties)
    def lettuceConnectionFactory = Mock(LettuceConnectionFactory)

    @Subject
    RedisCacheConfig redisCacheConfig

    def setup() {
        redisCacheConfig = new RedisCacheConfig(cacheProperties)
        setPrivateField(redisCacheConfig, "defaultCacheTtl", "86400")
    }

    def "should not return null from createDefaultCacheConfig"() {
        given: "cache properties returns a valid configuration"
        def validCacheConfig = RedisCacheConfiguration.defaultCacheConfig()
        cacheProperties.createCacheConfig(_) >> validCacheConfig

        when: "creating default cache config"
        def result = redisCacheConfig.createDefaultCacheConfig()

        then: "result should not be null"
        result != null
        result instanceof RedisCacheConfiguration
    }

    def "should throw exception when cacheProperties.createCacheConfig returns null"() {
        given: "cache properties returns null"
        cacheProperties.createCacheConfig(_) >> null

        when: "creating default cache config"
        def result = redisCacheConfig.createDefaultCacheConfig()

        then: "result should be null (which will cause RedisCacheManager to fail)"
        result == null
    }

    def "should fail to create RedisCacheManager when default config is null"() {
        given: "cache properties returns null configuration"
        cacheProperties.getInitialCacheConfigurations() >> [:]
        cacheProperties.createCacheConfig(_) >> null

        when: "creating Redis cache manager with null default config"
        redisCacheConfig.customRedisCacheManager(lettuceConnectionFactory)

        then: "should throw IllegalArgumentException with 'must not be null' message"
        def exception = thrown(IllegalArgumentException)
        exception.message.contains("must not be null") || exception.message.contains("DefaultCacheConfiguration")
    }

    def "should successfully create RedisCacheManager when default config is valid"() {
        given: "cache properties returns valid configuration"
        def validCacheConfig = RedisCacheConfiguration.defaultCacheConfig()
        cacheProperties.getInitialCacheConfigurations() >> [:]
        cacheProperties.createCacheConfig(_) >> validCacheConfig

        when: "creating Redis cache manager with valid default config"
        def result = redisCacheConfig.customRedisCacheManager(lettuceConnectionFactory)

        then: "should create RedisCacheManager successfully"
        result != null
        result instanceof org.springframework.data.redis.cache.RedisCacheManager
    }

    def "should handle cacheProperties throwing exception"() {
        given: "cache properties throws exception"
        cacheProperties.createCacheConfig(_) >> { throw new RuntimeException("Cache config error") }

        when: "creating default cache config"
        redisCacheConfig.createDefaultCacheConfig()

        then: "should propagate the exception"
        thrown(RuntimeException)
    }

    def "should verify TTL is passed correctly to cacheProperties"() {
        given: "cache properties mock"
        def validCacheConfig = RedisCacheConfiguration.defaultCacheConfig()
        cacheProperties.createCacheConfig(_) >> validCacheConfig

        when: "creating default cache config"
        redisCacheConfig.createDefaultCacheConfig()

        then: "should call createCacheConfig with correct duration"
        1 * cacheProperties.createCacheConfig({ Duration duration ->
            duration.seconds == 86400
        })
    }

    def "should test with different TTL values to ensure proper duration creation"() {
        given: "different TTL values"
        setPrivateField(redisCacheConfig, "defaultCacheTtl", ttlValue)
        def validCacheConfig = RedisCacheConfiguration.defaultCacheConfig()
        cacheProperties.createCacheConfig(_) >> validCacheConfig

        when: "creating default cache config"
        redisCacheConfig.createDefaultCacheConfig()

        then: "should call createCacheConfig with correct duration"
        1 * cacheProperties.createCacheConfig({ Duration duration ->
            duration.seconds == expectedSeconds
        })

        where:
        ttlValue | expectedSeconds
        "3600"   | 3600
        "7200"   | 7200
        "0"      | 0
        "1"      | 1
    }

    def "should verify cacheProperties is not null"() {
        expect: "cacheProperties should be injected and not null"
        def cachePropertiesField = RedisCacheConfig.class.getDeclaredField("cacheProperties")
        cachePropertiesField.setAccessible(true)
        def injectedCacheProperties = cachePropertiesField.get(redisCacheConfig)
        injectedCacheProperties != null
    }

    def "should test complete flow with valid configurations"() {
        given: "valid cache configurations"
        def validDefaultConfig = RedisCacheConfiguration.defaultCacheConfig()
        def validSpecificConfig = RedisCacheConfiguration.defaultCacheConfig()
        def initialConfigs = ["test-cache": validSpecificConfig]
        
        cacheProperties.createCacheConfig(_) >> validDefaultConfig
        cacheProperties.getInitialCacheConfigurations() >> initialConfigs

        when: "creating complete Redis cache manager"
        def result = redisCacheConfig.customRedisCacheManager(lettuceConnectionFactory)

        then: "should create manager successfully with all configurations"
        result != null
        1 * cacheProperties.createCacheConfig(_)
        1 * cacheProperties.getInitialCacheConfigurations()
    }

    def "should handle empty initial configurations"() {
        given: "empty initial configurations but valid default config"
        def validDefaultConfig = RedisCacheConfiguration.defaultCacheConfig()
        
        cacheProperties.createCacheConfig(_) >> validDefaultConfig
        cacheProperties.getInitialCacheConfigurations() >> [:]

        when: "creating Redis cache manager with empty initial configs"
        def result = redisCacheConfig.customRedisCacheManager(lettuceConnectionFactory)

        then: "should create manager successfully"
        result != null
    }

    def "should handle null initial configurations"() {
        given: "null initial configurations but valid default config"
        def validDefaultConfig = RedisCacheConfiguration.defaultCacheConfig()
        
        cacheProperties.createCacheConfig(_) >> validDefaultConfig
        cacheProperties.getInitialCacheConfigurations() >> null

        when: "creating Redis cache manager with null initial configs"
        def result = redisCacheConfig.customRedisCacheManager(lettuceConnectionFactory)

        then: "should handle null gracefully or throw appropriate exception"
        // This might throw NullPointerException or be handled gracefully
        (result != null) || thrown(NullPointerException)
    }

    def "should verify the exact error message for null default configuration"() {
        given: "cache properties returns null for default config"
        cacheProperties.getInitialCacheConfigurations() >> [:]
        cacheProperties.createCacheConfig(_) >> null

        when: "creating Redis cache manager"
        redisCacheConfig.customRedisCacheManager(lettuceConnectionFactory)

        then: "should throw IllegalArgumentException with specific message"
        def exception = thrown(IllegalArgumentException)
        exception.message.toLowerCase().contains("must not be null") || 
        exception.message.contains("DefaultCacheConfiguration")
    }

    private void setPrivateField(Object target, String fieldName, Object value) {
        def field = target.class.getDeclaredField(fieldName)
        field.setAccessible(true)
        field.set(target, value)
    }
}
