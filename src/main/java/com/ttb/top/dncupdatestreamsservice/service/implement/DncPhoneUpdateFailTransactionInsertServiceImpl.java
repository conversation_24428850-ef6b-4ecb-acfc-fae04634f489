package com.ttb.top.dncupdatestreamsservice.service.implement;

import com.mongodb.MongoCommandException;
import com.mongodb.MongoWriteException;
import com.ttb.top.dncupdatestreamsservice.entity.DncPhoneUpdateFailEntity;
import com.ttb.top.dncupdatestreamsservice.mapper.DncPhoneUpdateFailMapper;
import com.ttb.top.dncupdatestreamsservice.model.mongo.dncupdatephonefail.DncPhoneUpdateFailTransaction;
import com.ttb.top.dncupdatestreamsservice.repository.DncPhoneUpdateTransactionRepository;
import com.ttb.top.dncupdatestreamsservice.service.DncPhoneUpdateFailTransactionInsertService;
import com.ttb.top.library.exceptionmodel.constant.ResponseCodeEnum;
import com.ttb.top.library.exceptionmodel.exception.GenericException;
import com.ttb.top.library.httpheaderhelper.constant.HttpHeaderConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.mongodb.UncategorizedMongoDbException;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;

@Slf4j
@Service
@RequiredArgsConstructor
public class DncPhoneUpdateFailTransactionInsertServiceImpl implements
        DncPhoneUpdateFailTransactionInsertService {

    private final DncPhoneUpdateTransactionRepository dncPhoneUpdateTransactionRepository;

    @Override
    @Transactional
    public void insertTransactionDncPhoneUpdateFail(HttpHeaders headers,
                                                    DncPhoneUpdateFailTransaction dncPhoneUpdateFailTransaction,
                                                    String rmId,
                                                    String ecId) {

        DncPhoneUpdateFailEntity dncPhoneUpdateFailEntity = buildMongo(
                headers,
                dncPhoneUpdateFailTransaction,
                rmId,
                ecId);
        try {
            dncPhoneUpdateTransactionRepository.save(dncPhoneUpdateFailEntity);
        } catch (Exception e) {
            if (e instanceof DataIntegrityViolationException && e.getCause() instanceof MongoWriteException
                || e instanceof UncategorizedMongoDbException && e.getCause() instanceof MongoCommandException
                    && (((MongoCommandException) e.getCause()).getErrorCode() == 112)) {
                throw new GenericException(ResponseCodeEnum.DATABASE_ERROR_CODE);
            }
            throw new GenericException(e);
        }
    }

    private DncPhoneUpdateFailEntity buildMongo(HttpHeaders headers,
                                                DncPhoneUpdateFailTransaction dncPhoneUpdateFailTransaction,
                                                String rmId,
                                                String ecId) {

        DncPhoneUpdateFailTransaction buildDncPhoneUpdateFailTransaction = dncPhoneUpdateFailTransaction.toBuilder()
                .correlationId(headers.getFirst(HttpHeaderConstant.X_CORRELATION_ID))
                .ecId(ecId)
                .rmId(rmId)
                .modifiedOn(LocalDateTime.now()
                        .atOffset(ZoneOffset.UTC)
                        .format(DateTimeFormatter.ISO_INSTANT))
                .createdOn(LocalDateTime.now()
                        .atOffset(ZoneOffset.UTC)
                        .format(DateTimeFormatter.ISO_INSTANT))
                .build();

        return DncPhoneUpdateFailMapper.INSTANCE
                .mapDncPhoneUpdateFailToEntity(buildDncPhoneUpdateFailTransaction);
    }

}
