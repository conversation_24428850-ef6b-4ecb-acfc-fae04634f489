package com.ttb.top.dncupdatestreamsservice.service.implement;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ttb.top.dncupdatestreamsservice.constants.Constants;
import com.ttb.top.dncupdatestreamsservice.feign.customerdata.CustomerDataServiceFeignClient;
import com.ttb.top.dncupdatestreamsservice.model.feign.customerdata.customerprofile.request.CustomerProfileRequest;
import com.ttb.top.dncupdatestreamsservice.model.feign.customerdata.customerprofile.response.CustomerProfileResponse;
import com.ttb.top.dncupdatestreamsservice.model.feign.customerdata.updatephonenumbers.request.UpdatePhoneNumbersRequest;
import com.ttb.top.dncupdatestreamsservice.model.mongo.dncupdatephonefail.DncPhoneUpdateFailTransaction;
import com.ttb.top.dncupdatestreamsservice.model.streams.consumer.DncUpdateConsume;
import com.ttb.top.dncupdatestreamsservice.model.streams.producer.DncUpdateFailProducer;
import com.ttb.top.dncupdatestreamsservice.service.CustomerCacheService;
import com.ttb.top.dncupdatestreamsservice.service.DncPhoneUpdateFailTransactionInsertService;
import com.ttb.top.dncupdatestreamsservice.service.DncUpdateConsumerService;
import com.ttb.top.dncupdatestreamsservice.streams.producer.EventHubProducer;
import com.ttb.top.library.commonmodel.model.ResponseModel;
import com.ttb.top.library.exceptionmodel.constant.ResponseCodeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class DncUpdateConsumerServiceImpl implements DncUpdateConsumerService {
    private final ObjectMapper objectMapper;
    private final CustomerDataServiceFeignClient customerDataServiceFeignClient;
    private final CustomerCacheService customerCacheService;
    private final DncPhoneUpdateFailTransactionInsertService dncPhoneUpdateFailTransactionInsertService;
    private final RetryUpdateDncPhoneServiceImpl retryUpdateDncPhoneService;
    private final EventHubProducer<String> producer;
    CustomerProfileResponse customerProfileResponse = null;

    @Override
    public void processMessage(String message, Acknowledgment ack) {
        try {
            HashMap<String, String> data = objectMapper.readValue(message,
                    new TypeReference<HashMap<String, String>>() {});

            HttpHeaders headers = objectMapper.readValue(data.get("headers"), HttpHeaders.class);
            DncUpdateConsume dncUpdateConsume = objectMapper.readValue(data.get("dncUpdateConsumer"),
                    DncUpdateConsume.class);

            String crmId = dncUpdateConsume.getProfile().getRmId() == null
                    ? dncUpdateConsume.getProfile().getEcId() : dncUpdateConsume.getProfile().getRmId();

            // get customer profile from cache redis
            customerProfileResponse = customerCacheService.getCustomer(crmId);

            if (customerProfileResponse == null) {

                CustomerProfileRequest customerProfileRequest = dncUpdateConsume.getProfile().getEcId() != null
                        ? CustomerProfileRequest.builder().ecId(dncUpdateConsume.getProfile().getEcId()).build() :
                          CustomerProfileRequest.builder().rmId(dncUpdateConsume.getProfile().getRmId()).build();

                ResponseModel<CustomerProfileResponse> customerProfile =
                        customerDataServiceFeignClient.customerProfile(headers, customerProfileRequest);
                if (!Objects.equals(customerProfile.getCode(), "0000")) {

                    //TEP Service processing error
                    List<DncPhoneUpdateFailTransaction.Body> bodyList = dncUpdateConsume.getBody().stream()
                            .map(body -> DncPhoneUpdateFailTransaction.Body.builder()
                                    .dncListId(body.getProductGroupId())
                                    .dncList(DncPhoneUpdateFailTransaction.DncList.builder()
                                        .action(body.getDncList().getAction())
                                        .phoneNumbers(Arrays.stream(listPhoneNumber(customerProfile
                                                .getDataObj())).toList())
                                        .expirationDateTime(body.getDncList().getExpirationDateTime())
                                        .build())
                                    .build())
                            .collect(Collectors.toList());

                    DncPhoneUpdateFailTransaction.Error errorObject = DncPhoneUpdateFailTransaction.Error.builder()
                                    .serviceName(Constants.POST_DNCLIST_PHONENUMBER_ENDPOINT)
                                    .httpStatus(ResponseCodeEnum.LEGACY_SYSTEM_UNSUCCESS_ERROR_CODE.getCode())
                                    .code(customerProfile.getCode())
                                    .message(customerProfile.getHeader())
                                    .status(customerProfile.getStatus().getDescription())
                                    .build();
                    List<DncPhoneUpdateFailTransaction.Error> errorList = new ArrayList<>();
                    errorList.add(errorObject);

                    DncPhoneUpdateFailTransaction dncPhoneUpdateFailTransaction =
                            DncPhoneUpdateFailTransaction.builder()
                            .dncBody(bodyList)
                            .dncError(errorList)
                            .build();

                    dncPhoneUpdateFailTransactionInsertService.insertTransactionDncPhoneUpdateFail(
                        headers,
                        dncPhoneUpdateFailTransaction,
                        customerProfileRequest.getRmId(),
                        customerProfileRequest.getEcId());
                }

                customerProfileResponse = customerProfile.getDataObj();
                customerCacheService.setCustomer(crmId, customerProfileResponse);
            }

            // update DNC mobile number process
            // map phone number

            List<DncUpdateFailProducer> dncUpdateFailList = new ArrayList<>();

            dncUpdateConsume.getBody().forEach(item -> {
                UpdatePhoneNumbersRequest updatePhoneNumbersRequest = null;
                if ((item.getDncList().getExpirationDateTime() == null)
                        || item.getDncList().getExpirationDateTime()
                        .equalsIgnoreCase("4000-12-31T23:59Z")) {
                    updatePhoneNumbersRequest = UpdatePhoneNumbersRequest.builder()
                        .action(item.getDncList().getAction())
                        .phoneNumbers(Arrays.stream(listPhoneNumber(customerProfileResponse)).toList())
                        .build();
                } else {
                    updatePhoneNumbersRequest = UpdatePhoneNumbersRequest.builder()
                        .action(item.getDncList().getAction())
                        .phoneNumbers(Arrays.stream(listPhoneNumber(customerProfileResponse)).toList())
                        .expirationDateTime(item.getDncList().getExpirationDateTime())
                        .build();
                }

                //POST v1/customer-data-service/customers/dnclist/phonenumbers/{productgroupId}
                DncUpdateFailProducer dncUpdateFailProducer = retryUpdateDncPhoneService.callUpdateDncPhoneWithRetry(
                        headers, item.getProductGroupId(), updatePhoneNumbersRequest,
                        customerProfileResponse.getCustomer().getProfile().getRmId(),
                        customerProfileResponse.getCustomer().getProfile().getCcId());

                dncUpdateFailList.add(dncUpdateFailProducer);

            });

            DncUpdateFailProducer dncUpdateFailProducer = DncUpdateFailProducer.builder()
                    .body(new ArrayList<DncUpdateFailProducer.DncUpdatePhone>())
                    .errors(new ArrayList<DncUpdateFailProducer.DncUpdateError>())
                    .build();
            dncUpdateFailList.forEach(item -> {
                dncUpdateFailProducer.getBody().addAll(item.getBody());
                dncUpdateFailProducer.getErrors().addAll(item.getErrors());
            });

            //TEP Service processing error
            List<DncPhoneUpdateFailTransaction.Body> bodyList = dncUpdateFailProducer.getBody().stream()
                    .map(body -> DncPhoneUpdateFailTransaction.Body.builder()
                            .dncListId(body.getDncListId())
                            .dncList(DncPhoneUpdateFailTransaction.DncList.builder()
                                    .action(body.getDncList().getAction())
                                    .phoneNumbers(body.getDncList().getPhoneNumbers())
                                    .expirationDateTime(body.getDncList().getExpirationDateTime())
                                    .build())
                            .build())
                    .collect(Collectors.toList());

            List<DncPhoneUpdateFailTransaction.Error> errorList = dncUpdateFailProducer.getErrors().stream()
                    .map(body -> DncPhoneUpdateFailTransaction.Error.builder()
                            .serviceName(Constants.POST_DNCLIST_PHONENUMBER_ENDPOINT)
                            .httpStatus(body.getHttpStatus())
                            .code(body.getCode())
                            .message(body.getMessage())
                            .status(body.getStatus())
                            .build())
                    .collect(Collectors.toList());

            DncPhoneUpdateFailTransaction dncPhoneUpdateFailTransaction = DncPhoneUpdateFailTransaction.builder()
                    .dncBody(bodyList)
                    .dncError(errorList)
                    .build();

            dncPhoneUpdateFailTransactionInsertService.insertTransactionDncPhoneUpdateFail(
                    headers,
                    dncPhoneUpdateFailTransaction,
                    dncUpdateConsume.getProfile().getRmId(),
                    dncUpdateConsume.getProfile().getEcId());

            // success application log
            log.info("success");

        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    private String[]  listPhoneNumber(CustomerProfileResponse customerProfileResponse) {
        return customerProfileResponse.getCustomer().getPhones().stream()
                .filter(phone -> phone != null && Arrays.asList(new String[]{"M", "B", "R"})
                        .contains(phone.getPhoneType()))
                .map(phone -> phone.getPhoneNo())
                .filter(Objects::nonNull)
                .toArray(String[]::new);
    }
}
