package com.ttb.top.dncupdatestreamsservice.configuration;

import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cache.CacheManager;
import org.springframework.cache.support.NoOpCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;

import java.time.Duration;
import java.util.Map;

@Configuration("customerRedisCacheConfig")
@RequiredArgsConstructor
public class RedisCacheConfig {
    @Value("${cache.default.ttl:86400}")
    private String defaultCacheTtl;

    private final CacheProperties cacheProperties;

    @Bean("customRedisCacheManager")
    @ConditionalOnProperty(
            name = "redis.enabled",
            havingValue = "false",
            matchIfMissing = true
    )
    public CacheManager noOpCacheManager() {
        return new NoOpCacheManager();
    }

    @Bean("customRedisCacheManager")
    @ConditionalOnProperty(
            name = "redis.enabled",
            havingValue = "true"
    )
    public RedisCacheManager customRedisCacheManager(LettuceConnectionFactory lettuceConnectionFactory) {
        Map<String, RedisCacheConfiguration> initialCacheConfigurations =
                cacheProperties.getInitialCacheConfigurations();

        return RedisCacheManager.builder(lettuceConnectionFactory)
                .cacheDefaults(createDefaultCacheConfig())
                .withInitialCacheConfigurations(initialCacheConfigurations)
                .build();
    }

    private RedisCacheConfiguration createDefaultCacheConfig() {
        return cacheProperties.createCacheConfig(Duration.ofSeconds(Long.parseLong(defaultCacheTtl)));
    }
}
