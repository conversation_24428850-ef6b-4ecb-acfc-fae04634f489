package com.ttb.top.dncupdatestreamsservice.configuration;

import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.jsontype.impl.StdTypeResolverBuilder;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;
import java.time.LocalTime;
import java.util.HashMap;
import java.util.Map;

@Setter
@Configuration
@ConfigurationProperties(prefix = "cache.customer360")
public class CacheProperties {
    private Map<String, String> expireAtTime = new HashMap<>();
    private Map<String, Long> expireSeconds = new HashMap<>();

    public Map<String, RedisCacheConfiguration> getInitialCacheConfigurations() {
        Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();

        expireAtTime.forEach((key, expireTime) -> {
            LocalTime expirationTime = LocalTime.parse(expireTime);
            long secondsUntilExpiration = calculateSecondsUntilExpiration(expirationTime);
            cacheConfigurations.put(key, createCacheConfig(Duration.ofSeconds(secondsUntilExpiration)));
        });

        expireSeconds.forEach((key, ttl) -> {
            cacheConfigurations.put(key, createCacheConfig(Duration.ofSeconds(ttl)));
        });

        return cacheConfigurations;
    }

    public RedisCacheConfiguration createCacheConfig(Duration ttl) {
        return RedisCacheConfiguration.defaultCacheConfig()
                .disableKeyPrefix()
                .entryTtl(ttl)
                .serializeKeysWith(getKeySerializerPair())
                .serializeValuesWith(getValueSerializerPair());
    }

    private long calculateSecondsUntilExpiration(LocalTime expirationTime) {
        long secondsUntilExpiration = Duration.between(LocalTime.now(), expirationTime).getSeconds();
        if (secondsUntilExpiration < 0) {
            secondsUntilExpiration += Duration.ofDays(1).getSeconds();
        }
        return secondsUntilExpiration;
    }

    private RedisSerializationContext.SerializationPair<String> getKeySerializerPair() {
        return RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer());
    }

    private RedisSerializationContext.SerializationPair<?> getValueSerializerPair() {
        return RedisSerializationContext.SerializationPair
                .fromSerializer(new GenericJackson2JsonRedisSerializer(getSerializerMapper()));
    }

    private ObjectMapper getSerializerMapper() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.registerModule(new JavaTimeModule());
        GenericJackson2JsonRedisSerializer.registerNullValueSerializer(mapper, null);
        StdTypeResolverBuilder typeResolver =
                (new ObjectMapper.DefaultTypeResolverBuilder(ObjectMapper.DefaultTyping.EVERYTHING,
                        mapper.getPolymorphicTypeValidator())
                )
                        .init(JsonTypeInfo.Id.CLASS, null)
                        .inclusion(JsonTypeInfo.As.PROPERTY);
        mapper.setDefaultTyping(typeResolver);

        return mapper;
    }
}
