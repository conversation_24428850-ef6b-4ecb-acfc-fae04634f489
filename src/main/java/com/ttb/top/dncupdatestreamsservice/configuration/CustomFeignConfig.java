package com.ttb.top.dncupdatestreamsservice.configuration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ttb.top.library.requestloghelper.constant.RestInterceptorConstants;
import com.ttb.top.library.requestloghelper.model.RequestLogMessage;
import com.ttb.top.library.requestloghelper.service.RequestLogPublisher;
import com.ttb.top.library.requestloghelper.util.HeaderUtils;
import com.ttb.top.library.requestloghelper.util.MaskingUtil;
import feign.Request;
import feign.Response;
import feign.Util;
import feign.slf4j.Slf4jLogger;
import jakarta.annotation.Nullable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.retry.annotation.EnableRetry;

import java.net.URI;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.Map;
import java.util.Optional;

@Configuration
@EnableRetry
public class CustomFeignConfig extends Slf4jLogger {
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ResponseDTO {
        private int status;
        private String uri;
        private Map<String, Collection<String>> headers;
        private String body;
    }

    @Value("${app.api.logging.max-length}")
    private int limitContent;
    @Value("${app.api.logging.feign.exclude-url-patterns:}")
    private String[] excludedEndpoints;
    @Value("${app.api.logging.feign.enable:true}")
    private boolean feignEnable;
    @Value("${request-log.topic:}")
    private String topic;

    private static final Logger log = LoggerFactory.getLogger(CustomFeignConfig.class);
    private static final ObjectMapper mapper = new ObjectMapper();

    @Autowired
    private RequestLogPublisher publisher;
    @Autowired
    private MaskingUtil maskingUtil;

    @Bean
    public Level feignLoggerLevel() {
        return Level.BASIC;
    }

    public boolean isShouldNotFilter(String url) {
        boolean shouldNotFilter = true;
        if (url != null) {
            shouldNotFilter = Arrays.stream(this.excludedEndpoints)
                    .anyMatch((e) -> StringUtils.contains(url, e));
        }

        return shouldNotFilter;
    }

    @Nullable
    protected Response logAndRebufferResponse(String configKey, Level logLevel, Response response, long elapsedTime) {
        byte[] bodyData = null;

        try {
            bodyData = Util.toByteArray(response.body().asInputStream());
            boolean shouldNotFilter = this.isShouldNotFilter(response.request().url());
            if (this.feignEnable && !shouldNotFilter) {
                this.logHandler(response, bodyData, elapsedTime);
            }
            logInbound(response, bodyData);
        } catch (Exception var8) {
            log.error("Error while executing logAndRebufferResponse from feign", var8);
        }

        return response.toBuilder().body(bodyData).build();
    }

    private void logInbound(Response response, byte[] bodyData) {
        try {
            String decodedUri = URLDecoder.decode(response.request().url(), StandardCharsets.UTF_8.name());
            String bodyString = new String(bodyData, StandardCharsets.UTF_8);
            String truncatedBody = truncateString(bodyString, limitContent);
            ResponseDTO responseDTO = new ResponseDTO(
                    response.status(),
                    decodedUri,
                    response.headers(),
                    truncatedBody
            );
            log.info("PAYLOAD Inbound {}", mapper.writeValueAsString(responseDTO));
        } catch (Exception e) {
            log.error("PAYLOAD Inbound : Error while converting RequestLogMessage to JSON string");
        }
    }

    private String truncateString(String input, int maxLength) {
        if (input == null) {
            return "";
        }
        if (input.length() <= maxLength) {
            return input;
        } else {
            return input.substring(0, maxLength) + "..."; // เพิ่ม "..." เพื่อบ่งชี้ว่าข้อมูลถูกตัดขนาด
        }
    }

    private void logHandler(Response response, byte[] bodyData, long elapsedTime) {
        try {
            RequestLogMessage logRequestLogMessage = new RequestLogMessage();
            logRequestLogMessage.setRespHttpStatus(response.status());
            logRequestLogMessage.setFullPath(response.request().url());
            this.setResponse(logRequestLogMessage, response, bodyData, elapsedTime);
            this.setRequest(logRequestLogMessage, response.request());
            logRequestLogMessage.setService(response.request().requestTemplate().feignTarget().name());
            this.publisher.publishMessage(this.topic, logRequestLogMessage);
        } catch (Exception e) {
            log.error("Error while execute logHandler from feign", e);
        }
    }

    private RequestLogMessage setRequest(RequestLogMessage requestMessage, Request request) {
        try {
            requestMessage.setTranTime(new Date(System.currentTimeMillis()));
            if (Optional.ofNullable(request.body()).isPresent()) {
                requestMessage.setReqBody(
                        this.limitContent(
                                this.maskingUtil.maskJsonString(
                                        new String(request.body(), RestInterceptorConstants.UTF_8)
                                )
                        )
                );
                requestMessage.setReqContentLength(requestMessage.getReqBody().length());
            }

            requestMessage.setReqHeaders(
                    this.maskingUtil.maskMap(HeaderUtils.getHeadersFromCollections(request.headers()))
            );
            requestMessage.setReqMethod(request.httpMethod().name());
            requestMessage.setUri(
                    this.removeHostFromURI(
                            request.requestTemplate().feignTarget().url(),
                            request.requestTemplate().path())
            );
            requestMessage.setRawUri(
                    this.removeHostFromURI(request.requestTemplate().feignTarget().url(),
                            request.requestTemplate().path())
            );
            URI uri = this.getURI(request.url());
            requestMessage.setServerName(uri.getHost());
            requestMessage.setServerPort(uri.getPort());
            requestMessage.setReqQueryParams(this.getQueryParams(request.requestTemplate().queryLine()));
            requestMessage.setCorrelationId(HeaderUtils.getCorrelationId(request.headers()));

            logOutboundRequest(uri, request);
        } catch (Exception e) {
            log.error("Error while mapping log request from feign", e);
        }

        return requestMessage;
    }

    private void logOutboundRequest(URI uri, Request request) {
        try {
            String decodedUri = URLDecoder.decode(uri.toString(), StandardCharsets.UTF_8.name());
            String bodyString = new String(request.body() != null
                    ? request.body()
                    : new byte[0], StandardCharsets.UTF_8);
            String truncatedBody = truncateString(bodyString, limitContent);
            ResponseDTO responseDTO = new ResponseDTO(
                    200,
                    decodedUri,
                    request.headers(),
                    truncatedBody
            );
            String jsonString = mapper.writeValueAsString(responseDTO);
            String truncatedJson = truncateString(jsonString, limitContent);
            log.info("PAYLOAD Outbound {}", truncatedJson);
        } catch (Exception e) {
            log.error("PAYLOAD Outbound : Error while converting RequestLogMessage to JSON string");
        }
    }

    private String getQueryParams(String params) {
        return StringUtils.isNotBlank(params)
                && StringUtils.startsWith(params, "?")
                ? params.substring(1) : null;
    }

    private RequestLogMessage setResponse(
            RequestLogMessage requestMessage, Response response, byte[] bodyData, long elapsedTime) {
        try {
            requestMessage.setRespBody(
                    this.limitContent(
                            this.maskingUtil.maskJsonString(new String(bodyData, RestInterceptorConstants.UTF_8))
                    )
            );
            requestMessage.setRespHttpStatus(response.status());
            requestMessage.setExecuteTime(elapsedTime);
            requestMessage.setReqHeaders(HeaderUtils.getHeadersFromCollections(response.request().headers()));
            requestMessage.setCorrelationId(HeaderUtils.getCorrelationId(response.request().headers()));
        } catch (Exception e) {
            log.error("Error while mapping log response from feign", e);
        }

        return requestMessage;
    }

    private String limitContent(String body) {
        String bodyString = body;
        if (bodyString != null && bodyString.length() > this.limitContent) {
            bodyString = bodyString.substring(0, this.limitContent);
        }

        return bodyString;
    }

    private URI getURI(String url) {
        return URI.create(url);
    }

    private String removeHostFromURI(String host, String uri) {
        return uri.replace(host, "");
    }
}
