package com.ttb.top.dncupdatestreamsservice.repository.implement;

import com.ttb.top.dncupdatestreamsservice.entity.DncPhoneUpdateFailEntity;
import com.ttb.top.dncupdatestreamsservice.repository.DncPhoneUpdateTransactionRepository;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Repository;

@Repository
public class DncPhoneUpdateTransactionRepositoryImpl extends
    GenericRepositoryImpl<DncPhoneUpdateFailEntity> implements
        DncPhoneUpdateTransactionRepository {

    protected DncPhoneUpdateTransactionRepositoryImpl(
        MongoTemplate mongoTemplate) {
        super(mongoTemplate);
    }
}
