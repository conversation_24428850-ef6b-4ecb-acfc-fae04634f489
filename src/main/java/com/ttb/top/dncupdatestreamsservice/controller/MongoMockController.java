package com.ttb.top.dncupdatestreamsservice.controller;

import com.ttb.top.dncupdatestreamsservice.constants.Constants;
import com.ttb.top.dncupdatestreamsservice.model.feign.customerdata.customerprofile.response.CustomerProfileResponse;
import com.ttb.top.dncupdatestreamsservice.model.mongo.dncupdatephonefail.DncPhoneUpdateFailTransaction;
import com.ttb.top.dncupdatestreamsservice.model.streams.producer.DncUpdateFailProducer;
import com.ttb.top.dncupdatestreamsservice.service.DncPhoneUpdateFailTransactionInsertService;
import com.ttb.top.library.httpheaderhelper.constant.HttpHeaderConstant;
import com.ttb.top.library.lookuphelper.helper.EnableLookup;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


@Slf4j
@EnableLookup
@RestController
@RequiredArgsConstructor
@RequestMapping("/mongo")
public class MongoMockController {

    private final DncPhoneUpdateFailTransactionInsertService dncPhoneUpdateFailTransactionInsertService;

    @PostMapping("/test")
    CustomerProfileResponse insertMongo() throws IOException {
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaderConstant.X_CORRELATION_ID, "testX-Cor");

        DncUpdateFailProducer.DncUpdatePhone bodyObject = DncUpdateFailProducer.DncUpdatePhone.builder()
                .dncListId("Test1")
                .dncList(new DncUpdateFailProducer.DncList())
                .build();
        List<DncUpdateFailProducer.DncUpdatePhone> deeBody = new ArrayList<>();
        deeBody.add(bodyObject);

        DncUpdateFailProducer.DncUpdateError dncUpdateFailProducer1 = DncUpdateFailProducer.DncUpdateError.builder()
                .httpStatus("TestHttpStatus1")
                .code("TestCode1")
                .message("TestMessage1")
                .status("TestStatus1")
                .build();

        DncUpdateFailProducer.DncUpdateError dncUpdateFailProducer2 = DncUpdateFailProducer.DncUpdateError.builder()
                .httpStatus("TestHttpStatus1")
                .code("TestCode1")
                .message("TestMessage1")
                .status("TestStatus1")
                .build();

        List<DncUpdateFailProducer.DncUpdateError> deeError = new ArrayList<>();
        deeError.add(dncUpdateFailProducer1);
        deeError.add(dncUpdateFailProducer2);

        DncUpdateFailProducer dncUpdateFailProducer = DncUpdateFailProducer.builder()
                .body(deeBody)
                .errors(deeError)
                .build();

        List<DncPhoneUpdateFailTransaction.Body> bodyList = dncUpdateFailProducer.getBody().stream()
                .map(body -> DncPhoneUpdateFailTransaction.Body.builder()
                        .dncListId(body.getDncListId())
                        .dncList(DncPhoneUpdateFailTransaction.DncList.builder()
                                .action(body.getDncList().getAction())
                                .phoneNumbers(body.getDncList().getPhoneNumbers())
                                .expirationDateTime(body.getDncList().getExpirationDateTime())
                                .build())
                        .build())
                .collect(Collectors.toList());

        List<DncPhoneUpdateFailTransaction.Error> errorList = dncUpdateFailProducer.getErrors().stream()
                .map(body -> DncPhoneUpdateFailTransaction.Error.builder()
                        .serviceName(Constants.POST_DNCLIST_PHONENUMBER_ENDPOINT)
                        .httpStatus(body.getHttpStatus())
                        .code(body.getCode())
                        .message(body.getMessage())
                        .status(body.getStatus())
                        .build())
                .collect(Collectors.toList());

        DncPhoneUpdateFailTransaction dncPhoneUpdateFailTransaction = DncPhoneUpdateFailTransaction.builder()
                .dncBody(bodyList)
                .dncError(errorList)
                .build();

        dncPhoneUpdateFailTransactionInsertService.insertTransactionDncPhoneUpdateFail(headers,
                dncPhoneUpdateFailTransaction, "TestRM", "TestEC");
        return null;
    }
}

